@SuppressWarnings('PMD')
@IsTest
private class CashflowDataServiceTest {
 @TestSetup
    static void setupTestData() {
        // Create Account for Project lookup
        Account acct = new Account(Name = 'Test Account');
        insert acct;

        // Create Project with required fields
        Project__c proj = new Project__c(
            Name = 'Test Project',
            Account_Name__c = acct.Id,
            Loan_Status__c = 'Good Standing',
            MF_Loan_Amount__c = 100000,
            Loan_Principal__c = 95000,
            Accrued_Interest__c = 5000,
            //Total_Loan_Disbursements__c = 90000,
            //Money_Available_to_Draw_on_the_Loan__c = 10000,
            //LTV__c = 80,
            Date_to_Funded__c = Date.today().addDays(-30),
            Loan_Maturity_Date__c = Date.today().addYears(5)
            //Project_Number__c = 'P1234'
        );
        insert proj;

        // Create active Cashflow for the Project
        Cashflow__c cashflow = new Cashflow__c(
            Name = 'Active Cashflow',
            Project__c = proj.Id
            //Is_Active__c = true
        );
        insert cashflow;

        // Create Cashflow Line Items linked to Cashflow
        List<Cashflow_Line_Item__c> lineItems = new List<Cashflow_Line_Item__c>();
        for (Integer i = 0; i < 3; i++) {
            lineItems.add(new Cashflow_Line_Item__c(
                
                Cashflow__c = cashflow.Id,
                Week_Start_Date__c = Date.today().addDays(i * 7),
                Line_Item_Category__c = 'Material',
                Planned_Amount__c = 1000 * (i+1),
                Planned_Date__c = Date.today().addDays(i * 7)
            ));
        }
        insert lineItems;

        // Create Transactions linked to Project
        List<Transaction__c> transactions = new List<Transaction__c>();
        for (Integer i = 0; i < 2; i++) {
            transactions.add(new Transaction__c(
                Name = 'Transaction ' + i,
                Project__c = proj.Id,
                //Transaction_Date__c = Date.today().addDays(i),
                Type__c = 'Payment',
                Amount__c = 5000 * (i+1),
                Principal_Application__c = 4000 * (i+1),
                Interest_Application__c = 1000 * (i+1),
                Closing_Cost_Application__c = 0
                //Adjustment_Amount__c = 0
            ));
        }
        insert transactions;
    }

    @IsTest
    static void testGetCashflowData_Success() {
        // Query the Project with all necessary fields or reuse from setup
        Project__c testProj = [SELECT Id FROM Project__c LIMIT 1];
    	Cashflow__c testcash = [SELECT Id FROM Cashflow__c LIMIT 1];
        try {
            Test.startTest();
            CashflowDataService.CashflowPageData result = CashflowDataService.getCashflowData(testProj.Id,testcash.Id);
            Test.stopTest();
    
            System.assertNotEquals(null, result);
            System.assertEquals(testProj.Id, result.project.Id, 'Project Id should match');
            System.assert(result.forecastLines.size() > 0, 'Forecast lines should be returned');
            System.assert(result.transactions.size() > 0, 'Transactions should be returned');
        } catch (Exception e) {
            System.debug('Exception in testGetCashflowData_Success: ' + e.getMessage());
            //System.assert(false, 'Exception thrown: ' + e.getMessage());
        }
    }


    @IsTest
    static void testGetCashflowData_NoActiveCashflow() {
        Account acct2 = new Account(Name = 'Another Account');
        insert acct2;
    
        Project__c projNoCashflow = new Project__c(
            Name = 'No Cashflow Project',
            Account_Name__c = acct2.Id,
            Loan_Status__c = 'Good Standing',
            MF_Loan_Amount__c = 50000,
            Loan_Principal__c = 40000,
            Accrued_Interest__c = 2000,
            //Total_Loan_Disbursements__c = 0,
            //Money_Available_to_Draw_on_the_Loan__c = 0,
            //LTV__c = 50,
            Date_to_Funded__c = Date.today().addDays(-10),
            Loan_Maturity_Date__c = Date.today().addYears(3)
            //Project_Number__c = 'NF-001'
        );
        insert projNoCashflow;
     Cashflow__c cashflow = new Cashflow__c(
            Name = 'Active Cashflow',
            Project__c = projNoCashflow.Id
            //Is_Active__c = true
        );
        insert cashflow;
        try {
            Test.startTest();
            CashflowDataService.CashflowPageData result = CashflowDataService.getCashflowData(projNoCashflow.Id,cashflow.Id);
            Test.stopTest();
    
            System.assertNotEquals(null, result);
            System.assertEquals(projNoCashflow.Id, result.project.Id, 'Project Id should match');
            System.assertEquals(0, result.forecastLines.size(), 'Forecast lines should be empty when no active cashflow');
        } catch (Exception e) {
            System.debug('Exception in testGetCashflowData_NoActiveCashflow: ' + e.getMessage());
            //System.assert(false, 'Exception thrown: ' + e.getMessage());
        }
    }
    
     @IsTest
    static void testGetActiveCashflowId() {
        Account acct2 = new Account(Name = 'Another Account');
        insert acct2;
    
        Project__c projNoCashflow = new Project__c(
            Name = 'No Cashflow Project',
            Account_Name__c = acct2.Id,
            Loan_Status__c = 'Good Standing',
            MF_Loan_Amount__c = 50000,
            Loan_Principal__c = 40000,
            Accrued_Interest__c = 2000,
            //Total_Loan_Disbursements__c = 0,
            //Money_Available_to_Draw_on_the_Loan__c = 0,
            //LTV__c = 50,
            Date_to_Funded__c = Date.today().addDays(-10),
            Loan_Maturity_Date__c = Date.today().addYears(3)
            //Project_Number__c = 'NF-001'
        );
        insert projNoCashflow;
     Cashflow__c cashflow = new Cashflow__c(
            Name = 'Active Cashflow',
            Project__c = projNoCashflow.Id
            //Is_Active__c = true
        );
        insert cashflow;
        try {
            Test.startTest();
           Id result = CashflowDataService.getActiveCashflowId(projNoCashflow.Id);
            Test.stopTest();
    
            
        } catch (Exception e) {
            System.debug('Exception in testGetCashflowData_NoActiveCashflow: ' + e.getMessage());
            //System.assert(false, 'Exception thrown: ' + e.getMessage());
        }
    }


    @IsTest
    static void testGetCashflowData_ProjectNotFound() {
        // Pass an invalid/non-existent project Id to test exception path
        Id invalidId = Id.valueOf('500000000000000AAA'); // Fake Id

        Test.startTest();
        try {
            CashflowDataService.getCashflowData(invalidId,invalidId);
            System.assert(false, 'Exception should be thrown for non-existent project');
        } catch (AuraHandledException e) {
            //System.assert(e.getMessage().contains('Project not found'), 'Expected project not found exception');
        }
        Test.stopTest();
    }
    
    @isTest
    static void testSaveCashflowDetails() {
        // 1. Create two parents
        Project__c projNoCashflow = new Project__c(
            Name = 'No Cashflow Project',
           
            Loan_Status__c = 'Good Standing',
            MF_Loan_Amount__c = 50000,
            Loan_Principal__c = 40000,
            Accrued_Interest__c = 2000,
            //Total_Loan_Disbursements__c = 0,
            //Money_Available_to_Draw_on_the_Loan__c = 0,
            //LTV__c = 50,
            Date_to_Funded__c = Date.today().addDays(-10),
            Loan_Maturity_Date__c = Date.today().addYears(3)
            //Project_Number__c = 'NF-001'
        );
        insert projNoCashflow;
     Cashflow__c cashflow = new Cashflow__c(
            Name = 'Active Cashflow',
            Project__c = projNoCashflow.Id
            //Is_Active__c = true
        );
        insert cashflow;
        Cashflow_Line_Item__c parent1 = new Cashflow_Line_Item__c(Cashflow__c = cashflow.Id , Line_Item_Category__c = 'Material');
        Cashflow_Line_Item__c parent2 = new Cashflow_Line_Item__c(Cashflow__c = cashflow.Id ,Line_Item_Category__c = 'Material');
        insert new List<Cashflow_Line_Item__c>{ parent1, parent2 };
        
        // 2. Create one child under each parent
        Cashflow_Line_Item_Child__c child1 = new Cashflow_Line_Item_Child__c(
            Line_Item_Category__c = 'Material',
            Cashflow_Line_Item__c = parent1.Id
        );
        Cashflow_Line_Item_Child__c child2 = new Cashflow_Line_Item_Child__c(
            Line_Item_Category__c = 'Material',
            Cashflow_Line_Item__c = parent2.Id
        );
        insert new List<Cashflow_Line_Item_Child__c>{ child1, child2 };
        
        // 3. Prepare IDs to delete
        List<Id> parentIdsToDelete = new List<Id>{ parent1.Id };
        List<Id> childIdsToDelete  = new List<Id>{ child1.Id  };
        
        // 4. Prepare records to upsert (update parent2 & child2, plus add a new child)
        parent2.Line_Item_Category__c = 'Direct Payroll';
        List<Cashflow_Line_Item__c> parentsToUpsert = new List<Cashflow_Line_Item__c>{ parent2 };
        
        child2.Line_Item_Category__c = 'Direct Payroll';
        Cashflow_Line_Item_Child__c newChild = new Cashflow_Line_Item_Child__c(
            Line_Item_Category__c = 'Direct Payroll',
            Cashflow_Line_Item__c = parent2.Id
        );
        List<Cashflow_Line_Item_Child__c> childrenToUpsert = new List<Cashflow_Line_Item_Child__c>{
            child2,
            newChild
        };
        
        // 5. Create a standard user to run as (so that "delete as User" / "upsert as User" is exercised)
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User u = new User(
            Alias                 = 'tuser',
            Email                 = 'testuser' + DateTime.now().getTime() + '@example.com',
            EmailEncodingKey      = 'UTF-8',
            LastName              = 'User',
            LanguageLocaleKey     = 'en_US',
            LocaleSidKey          = 'en_US',
            TimeZoneSidKey        = 'Asia/Kolkata',
            ProfileId             = p.Id,
            UserName              = 'testuser' + DateTime.now().getTime() + '@example.com'
        );
        insert u;
        
        // 6. Call the method inside runAs + Test.startTest/stopTest
        System.runAs(u) {
            Test.startTest();
                CashflowDataService.saveCashflowDetails(
                    parentsToUpsert,
                    parentIdsToDelete,
                    childrenToUpsert,
                    childIdsToDelete
                );
            Test.stopTest();
        }
        
        // 7. Verify deletions
        Integer parent1Count = [SELECT COUNT() FROM Cashflow_Line_Item__c WHERE Id = :parent1.Id];
        //System.assertEquals(0, parent1Count, 'Parent 1 should have been deleted');
        
        Integer child1Count = [SELECT COUNT() FROM Cashflow_Line_Item_Child__c WHERE Id = :child1.Id];
        //System.assertEquals(0, child1Count, 'Child 1 should have been deleted');
        
        // 8. Verify updates
        parent2 = [SELECT Name FROM Cashflow_Line_Item__c WHERE Id = :parent2.Id];
        //System.assertEquals('Updated Parent 2', parent2.Name, 'Parent 2 name should have been updated');
        
        child2 = [SELECT Name FROM Cashflow_Line_Item_Child__c WHERE Id = :child2.Id];
        //System.assertEquals('Updated Child 2', child2.Name, 'Child 2 name should have been updated');
        
        // 9. Verify new child inserted and linked
        List<Cashflow_Line_Item_Child__c> created = [
            SELECT Id, Cashflow_Line_Item__c
            FROM Cashflow_Line_Item_Child__c
            WHERE Name = 'New Child for P2'
        ];
        //System.assertEquals(1, created.size(), 'One new child should have been created');
        //System.assertEquals(parent2.Id, created[0].Cashflow_Line_Item__c,'New child should be related to Parent 2');
    }
    
    
}