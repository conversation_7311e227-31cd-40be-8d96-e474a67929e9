@SuppressWarnings('PMD')
public with sharing class DisbursementFormController {

    @AuraEnabled(cacheable=true)
    public static Map<String, List<String>> getDisbursementPicklistValues() {
        DebugLogUtil.entry('DisbursementFormController.getDisbursementPicklistValues');
        Map<String, List<String>> picklistValues = new Map<String, List<String>>();
        List<String> expenseTypes = new List<String>();
        Schema.DescribeFieldResult fieldResult = Disbursement_Request__c.Disbursement_Type__c.getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        for (Schema.PicklistEntry p : ple) {
            expenseTypes.add(p.getValue());
        }
        picklistValues.put('expenseTypes', expenseTypes);
        DebugLogUtil.log('Fetched ' + expenseTypes.size() + ' disbursement types');
        DebugLogUtil.exit('DisbursementFormController.getDisbursementPicklistValues');
        return picklistValues;
    }

    @AuraEnabled
    public static Id submitDisbursementRequest(String requestWrapperJSON) {
        DebugLogUtil.entry('DisbursementFormController.submitDisbursementRequest', new Map<String, Object>{ 'requestWrapperJSON' => requestWrapperJSON });
        System.debug('DisbursementFormController.submitDisbursementRequest called with JSON: ' + requestWrapperJSON);
        RequestWrapper wrapper = (RequestWrapper) JSON.deserialize(requestWrapperJSON, RequestWrapper.class);
        Id newDisbursementRequestId;
        List<RequestItemController.SaveResultOutput> itemResults = null;
        String signatureContentDownloadUrl = null;

        try {
            // 1. Payee Information Handling
            DebugLogUtil.log('Handling Payee Information');
            Id payeeInfoId = wrapper.formDetails.selectedPayeeId;
            Payee_Information__c newPayee = new Payee_Information__c(
                Payee_Name__c = wrapper.formDetails.payeeName,
                Payee_First_Name__c = wrapper.formDetails.payeeFirstName,
                Payee_Last_Name__c = wrapper.formDetails.payeeLastName,
                Payee_Contact_Email__c = wrapper.formDetails.payeeContactEmail,
                Payment_Method__c = wrapper.formDetails.paymentMethod,
                Payee_Phone__c = wrapper.formDetails.payeePhone,
                Street__c = wrapper.formDetails.payeeStreet,
                City__c = wrapper.formDetails.payeeCity,
                State_Province__c = wrapper.formDetails.payeeState,
                Zip_Postal_Code__c = wrapper.formDetails.payeeZip,
                Country__c = wrapper.formDetails.payeeCountry,
                Mail_Check_To__c = wrapper.formDetails.mailCheckTo,
                Bank_Name__c = wrapper.formDetails.bankName,
                Account_Name__c = wrapper.formDetails.bankAccountName,
                Bank_Routing_Number__c = wrapper.formDetails.bankRoutingNumber,
                Bank_Account_Number__c = wrapper.formDetails.bankAccountNumber,
                Project__c = wrapper.initialDetails.projectId
            );
            if (payeeInfoId != null) {
                newPayee.Id = payeeInfoId;
            }
            upsert newPayee;
            DebugLogUtil.log('Upserted Payee_Information__c with Id: ' + newPayee.Id);

            // 2. Create Disbursement_Request__c
            DebugLogUtil.log('Creating Disbursement_Request__c');
            Disbursement_Request__c disbRequest = new Disbursement_Request__c(
                Loan_Number__c = wrapper.initialDetails.loanName,
                Project_lookup__c = wrapper.initialDetails.projectId,
                Project_Name__c = wrapper.initialDetails.projectNameForPerjury,
                Disbursement_Type__c = wrapper.formDetails.expenseType,
                Payee_Name__c = wrapper.formDetails.payeeName,
                Payee_Contact_Name__c = wrapper.formDetails.payeeFirstName + ' ' + wrapper.formDetails.payeeLastName,
                Payee_Contact_Email__c = wrapper.formDetails.payeeContactEmail,
                Payment_Method__c = wrapper.formDetails.paymentMethod,
                Phone__c = wrapper.formDetails.payeePhone,
                Street_Address__c = wrapper.formDetails.payeeStreet,
                City__c = wrapper.formDetails.payeeCity,
                State__c = wrapper.formDetails.payeeState,
                Zip_Code__c = wrapper.formDetails.payeeZip,
                Bank_Name__c = wrapper.formDetails.bankName,
                Account_Name__c = wrapper.formDetails.bankAccountName,
                Bank_Routing_Number__c = wrapper.formDetails.bankRoutingNumber,
                Bank_Account_Number__c = wrapper.formDetails.bankAccountNumber,
                Requester_Name__c = wrapper.formDetails.yourName,
                Requester_Email__c = wrapper.formDetails.requesterEmail,
                Comments__c = wrapper.formDetails.additionalComments,
                Status__c = 'New',
                Payee_Contact__c = wrapper.initialDetails.contactId,
                Client_Name__c = wrapper.initialDetails.accountName,
                General_Contractor_Contract_Owner__c = wrapper.initialDetails.gcOwnerNameForPerjury
            );
            if (String.isNotBlank(wrapper.formDetails.clientSignature)) {
                String base64Only = wrapper.formDetails.clientSignature.contains(',')
                    ? wrapper.formDetails.clientSignature.split(',')[1]
                    : wrapper.formDetails.clientSignature;
                disbRequest.Signature_Picture__c = '<img src="data:image/png;base64,' + base64Only + '">';
            }
            DebugLogUtil.log('disbRequest: ' + disbRequest);
            DebugLogUtil.log('disbRequest.Signature_Picture__c: ' + disbRequest.Signature_Picture__c);

            insert disbRequest;
            
            newDisbursementRequestId = disbRequest.Id;
            DebugLogUtil.log('Inserted Disbursement_Request__c with Id: ' + newDisbursementRequestId);

            // 3. Signature Upload
            if (String.isNotBlank(wrapper.formDetails.clientSignature)) {
                DebugLogUtil.log('Uploading signature via UploadFileFromFlow');
                UploadFileFromFlow.FileUploadRequest sigRequest = new UploadFileFromFlow.FileUploadRequest();
                sigRequest.base64Data = wrapper.formDetails.clientSignature.contains(',')
                    ? wrapper.formDetails.clientSignature.split(',')[1]
                    : wrapper.formDetails.clientSignature;
                sigRequest.linkedEntityId = newDisbursementRequestId;

                DebugLogUtil.log('sigRequest: ' + sigRequest);
                List<UploadFileFromFlow.FileUploadRequest> sigRequests = new List<UploadFileFromFlow.FileUploadRequest>{sigRequest};
                List<UploadFileFromFlow.FileUploadResponse> sigResponses = UploadFileFromFlow.uploadFile(sigRequests);
                DebugLogUtil.log('sigResponses: ' + sigResponses);
                
                if (sigResponses != null && !sigResponses.isEmpty() && sigResponses[0] != null && String.isNotBlank(sigResponses[0].contentDownloadUrl)) {
                    signatureContentDownloadUrl = sigResponses[0].contentDownloadUrl;
                    DebugLogUtil.log('Signature uploaded. URL: ' + signatureContentDownloadUrl);
                } else {
                    DebugLogUtil.warn('Signature upload did not return a URL or failed.');
                }
            }

            // 4. Main Uploaded Files Linking
            if (wrapper.mainUploadedDocumentIds != null && !wrapper.mainUploadedDocumentIds.isEmpty()) {
                DebugLogUtil.log('Linking main uploaded documents');
                List<ContentDocumentLink> cdlsToInsert = new List<ContentDocumentLink>();
                Set<Id> existingCdlDocIds = new Set<Id>();
                for (ContentDocumentLink c : [SELECT ContentDocumentId FROM ContentDocumentLink WHERE LinkedEntityId = :newDisbursementRequestId AND ContentDocumentId IN :wrapper.mainUploadedDocumentIds]) {
                    existingCdlDocIds.add(c.ContentDocumentId);
                }
                for (Id docId : wrapper.mainUploadedDocumentIds) {
                    if (!existingCdlDocIds.contains(docId)) {
                        ContentDocumentLink cdl = new ContentDocumentLink(
                            ContentDocumentId = docId,
                            LinkedEntityId = newDisbursementRequestId,
                            ShareType = 'V',
                            Visibility = 'AllUsers'
                        );
                        cdlsToInsert.add(cdl);
                    }
                }
                if (!cdlsToInsert.isEmpty()) {
                    insert cdlsToInsert;
                    DebugLogUtil.log(cdlsToInsert.size() + ' main files linked to Disbursement Request.');
                }
            }

            // 5. Save Requested Items and Link Their Files
            if (wrapper.requestItemsJSON != null && !wrapper.requestItemsJSON.isEmpty()) {
                DebugLogUtil.log('Saving requested items via RequestItemController');
                RequestItemController.SaveContext itemContext = new RequestItemController.SaveContext();
                itemContext.disbursementRequestId = newDisbursementRequestId;

                List<RequestItemController.RequestItemDetail> itemDetailsForApexAction = new List<RequestItemController.RequestItemDetail>();
                for (RequestItemWrapper itemWrapper : wrapper.requestItemsJSON) {
                    RequestItemController.RequestItemDetail detail = new RequestItemController.RequestItemDetail();
                    detail.Name = itemWrapper.Name;
                    detail.descriptionWork = itemWrapper.descriptionWork;
                    detail.invoiceDate = itemWrapper.invoiceDate;
                    detail.invoiceAmount = itemWrapper.invoiceAmount;
                    detail.invoice = itemWrapper.invoice;
                    detail.invoiceDueDate = itemWrapper.invoiceDueDate;
                    detail.contentDocumentIds = new List<String>();
                    if (itemWrapper.contentDocumentIds != null) {
                        for (Id docId : itemWrapper.contentDocumentIds) {
                            detail.contentDocumentIds.add(String.valueOf(docId));
                        }
                    }
                    itemDetailsForApexAction.add(detail);
                }
                itemContext.requestItemsJSON = JSON.serialize(itemDetailsForApexAction);

                List<RequestItemController.SaveContext> itemContexts = new List<RequestItemController.SaveContext>{itemContext};
                itemResults = RequestItemController.saveRequestItemsAndLinkFiles(itemContexts);

                if (itemResults == null || itemResults.isEmpty() || !itemResults[0].isSuccess) {
                    DebugLogUtil.warn('Saving requested items failed. Error: ' +
                        (itemResults != null && !itemResults.isEmpty() ? itemResults[0].errorMessage : 'No response'));}
                else {
                    DebugLogUtil.log('Requested items created: ' + itemResults[0].createdItems.size());
                }
            }

            // 6. Send Email
            DebugLogUtil.log('Sending email via sendEmailOfDisbursementReqForm');
            sendEmailOfDisbursementReqForm.inputVariables emailInput = new sendEmailOfDisbursementReqForm.inputVariables();
            emailInput.disbursementReqId = newDisbursementRequestId;
            emailInput.contentDownloadUrl = signatureContentDownloadUrl;
            if (itemResults != null && !itemResults.isEmpty() && itemResults[0].isSuccess && itemResults[0].createdItems != null) {
                emailInput.requestedItemIds = itemResults[0].createdItems;
            } else {
                emailInput.requestedItemIds = new List<Requested_Item__c>();
            }
            emailInput.noteId = new List<Note>();

            List<sendEmailOfDisbursementReqForm.inputVariables> emailInputs = new List<sendEmailOfDisbursementReqForm.inputVariables>{emailInput};
            List<sendEmailOfDisbursementReqForm.outputVariables> emailOutputs = sendEmailOfDisbursementReqForm.sendEmailOfDisbursementRequestForm(emailInputs);
            if (emailOutputs != null && !emailOutputs.isEmpty() && emailOutputs[0].result) {
                DebugLogUtil.log('Email sent successfully');
            } else {
                DebugLogUtil.warn('Email sending may have failed');
            }

            // 7. Link PDF of Agreement
            DebugLogUtil.log('Generating and attaching Agreement PDF via AdvanceRequestController');
            AdvanceRequestController.PDFRequest pdfReq = new AdvanceRequestController.PDFRequest();
            pdfReq.recordId = newDisbursementRequestId;
            pdfReq.advanceAmount = String.valueOf(wrapper.totalAmount);
            pdfReq.advanceDate = String.valueOf(Date.today());
            pdfReq.agreementDate = wrapper.initialDetails.effectiveDateofLoan != null
                ? String.valueOf(wrapper.initialDetails.effectiveDateofLoan.format()) : null;
            pdfReq.borrowerEntityName = wrapper.initialDetails.accountName;
            pdfReq.borrowerName = wrapper.initialDetails.accountName;
            pdfReq.contentDownloadUrl = signatureContentDownloadUrl;
            pdfReq.purpose = wrapper.initialDetails.projectNameForPerjury;
            pdfReq.signatoryName = wrapper.formDetails.yourName;
            pdfReq.signatoryTitle = wrapper.initialDetails.contactTitle;
            List<AdvanceRequestController.PDFRequest> pdfRequests = new List<AdvanceRequestController.PDFRequest>{pdfReq};
            AdvanceRequestController.generateAndAttachPDF(pdfRequests);
            DebugLogUtil.log('PDF generation initiated via AdvanceRequestController.');

        } catch (Exception e) {
            DebugLogUtil.error('Error in submitDisbursementRequest', e);
            System.debug('Error in submitDisbursementRequest: ' + e.getMessage() + ' Stack: ' + e.getStackTraceString());
            throw new AuraHandledException('Error processing disbursement request: ' + e.getMessage());
        }

        DebugLogUtil.exit('DisbursementFormController.submitDisbursementRequest');
        return newDisbursementRequestId;
    }

    @AuraEnabled(cacheable=true)
    public static String getPausedFormState(Id pausedFormId) {
        DebugLogUtil.entry('DisbursementFormController.getPausedFormState', new Map<String, Object>{ 'pausedFormId' => pausedFormId });
        try {
            Paused_Flow__c paused = [SELECT Id, Form_State_JSON__c FROM Paused_Flow__c WHERE Id = :pausedFormId AND Status__c = 'Paused' LIMIT 1];
            DebugLogUtil.log('Fetched paused form state for Id: ' + pausedFormId);
            DebugLogUtil.exit('DisbursementFormController.getPausedFormState');
            return paused.Form_State_JSON__c;
        } catch (Exception e) {
            DebugLogUtil.error('Error fetching paused form state', e);
            System.debug('Error fetching paused state for ID ' + pausedFormId + ': ' + e.getMessage());
            throw new AuraHandledException('Error fetching paused state: ' + e.getMessage());
        }
    }

    @AuraEnabled
    public static Id savePausedFormState(String jsonState, Id existingPausedId, Id projectId, String pauseReason) { // Added pauseReason
        DebugLogUtil.entry('DisbursementFormController.savePausedFormState', new Map<String, Object>{
            'existingPausedId' => existingPausedId,
            'projectId' => projectId,
            'pauseReason' => pauseReason // For logging
        });
        Paused_Flow__c flowToSave;
        try {
            if (existingPausedId != null) {
                flowToSave = [SELECT Id, Name, Project__c, Pause_Reason__c FROM Paused_Flow__c WHERE Id = :existingPausedId LIMIT 1]; // Added Pause_Reason__c
                DebugLogUtil.log('Updating existing paused flow: ' + existingPausedId);
            } else {
                flowToSave = new Paused_Flow__c();
                flowToSave.Project__c = projectId;
                // You might want to set a default Name for the Paused_Flow__c record if it's new
                // Example: flowToSave.Name = 'Paused Disbursement - ' + Datetime.now().format();
                DebugLogUtil.log('Creating new paused flow for project: ' + projectId);
            }
            flowToSave.Form_State_JSON__c = jsonState;
            flowToSave.Status__c = 'Paused';
            flowToSave.User__c = UserInfo.getUserId();
            flowToSave.Pause_Reason__c = pauseReason; // Save the pause reason

            upsert flowToSave;
            DebugLogUtil.log('Upserted Paused_Flow__c with Id: ' + flowToSave.Id);
            DebugLogUtil.exit('DisbursementFormController.savePausedFormState');
            return flowToSave.Id;
        } catch (Exception e) {
            DebugLogUtil.error('Error saving paused form state', e);
            System.debug('Error saving paused state: ' + e.getMessage());
            throw new AuraHandledException('Error saving paused state: ' + e.getMessage());
        }
    }

    @AuraEnabled
    public static void completePausedFormState(Id pausedFormId) {
        DebugLogUtil.entry('DisbursementFormController.completePausedFormState', new Map<String, Object>{ 'pausedFormId' => pausedFormId });
        try {
            Paused_Flow__c pf = new Paused_Flow__c(Id = pausedFormId, Status__c = 'Submitted');
            update pf;
            DebugLogUtil.log('Marked paused form as Submitted for Id: ' + pausedFormId);
        } catch (Exception e) {
            DebugLogUtil.error('Error updating paused form status to submitted', e);
            System.debug('Error updating paused form status to submitted for ID ' + pausedFormId + ': ' + e.getMessage());
        } finally {
            DebugLogUtil.exit('DisbursementFormController.completePausedFormState');
        }
    }

    public class RequestWrapper {
        @AuraEnabled public InitialDetailsWrapper initialDetails;
        @AuraEnabled public FormDetailsWrapper formDetails;
        @AuraEnabled public List<RequestItemWrapper> requestItemsJson;
        @AuraEnabled public List<Id> mainUploadedDocumentIds;
        @AuraEnabled public Decimal totalAmount;
    }

    public class InitialDetailsWrapper {
        @AuraEnabled public Id loanId;
        @AuraEnabled public String loanName;
        @AuraEnabled public Id projectId;
        @AuraEnabled public String projectName;
        @AuraEnabled public String accountName;
        @AuraEnabled public String contactName;
        @AuraEnabled public Id contactId;
        @AuraEnabled public String contactTitle;
        @AuraEnabled public Date effectiveDateofLoan;
        @AuraEnabled public String gcOwnerNameForPerjury;
        @AuraEnabled public String projectNameForPerjury;
    }

    public class FormDetailsWrapper {
        @AuraEnabled public String expenseType;
        @AuraEnabled public Id selectedPayeeId;
        @AuraEnabled public String payeeName;
        @AuraEnabled public String payeeFirstName;
        @AuraEnabled public String payeeLastName;
        @AuraEnabled public String payeeContactEmail;
        @AuraEnabled public String paymentMethod;
        @AuraEnabled public String payeeStreet;
        @AuraEnabled public String payeeCity;
        @AuraEnabled public String payeeState;
        @AuraEnabled public String payeeZip;
        @AuraEnabled public String payeeCountry;
        @AuraEnabled public String mailCheckTo;
        @AuraEnabled public String bankName;
        @AuraEnabled public String bankAccountName;
        @AuraEnabled public String bankRoutingNumber;
        @AuraEnabled public String bankAccountNumber;
        @AuraEnabled public String payeePhone;
        @AuraEnabled public String yourName;
        @AuraEnabled public String requesterEmail;
        @AuraEnabled public String additionalComments;
        @AuraEnabled public String clientSignature;
        @AuraEnabled public Boolean iAgreePerjury;
    }

    public class RequestItemWrapper {
        @AuraEnabled public String Name;
        @AuraEnabled public String descriptionWork;
        @AuraEnabled public String invoice;
        @AuraEnabled public Decimal invoiceAmount;
        @AuraEnabled public Date invoiceDate;
        @AuraEnabled public Date invoiceDueDate;
        @AuraEnabled public List<Id> contentDocumentIds;
    }
}