@SuppressWarnings('PMD.AvoidDeeplyNestedIfStmts, PMD.CognitiveComplexity, PMD.ExcessiveMethodLength,PMD')
public with sharing class CreateHistory {
   private static final String METHOD = 'CashflowLineItemHistoryHandler.captureHistory';
    // use a tag for all Nebula log entries from this process
    private static final List<String> TAGS   = new List<String>{ 'CashflowHistory' };

    /**
     * Creates history records only for records where key fields changed.
     *
     * @param oldMap   Map<Id, Cashflow_Line_Item__c> before-update
     * @param newMap   Map<Id, Cashflow_Line_Item__c> after-update
     */
    public static void captureHistory(
        Map<Id, Cashflow_Line_Item__c> oldMap,
        Map<Id, Cashflow_Line_Item__c> newMap
    ) {
        // entry log with total count
        DebugLogUtil.entry(METHOD,
            new Map<String, Object>{ 'totalUpdates' => newMap.size() },
            TAGS
        );

        List<Cashflow_Line_Item_History__c> historyToInsert = new List<Cashflow_Line_Item_History__c>();

        for (Id recId : newMap.keySet()) {
            Cashflow_Line_Item__c oldItem = oldMap.get(recId);
            Cashflow_Line_Item__c newItem = newMap.get(recId);

            // skip if none of the tracked fields changed
            if (
                oldItem.Line_Item_Category__c           == newItem.Line_Item_Category__c
             && oldItem.Material_Amount_Variation__c  == newItem.Material_Amount_Variation__c
             && oldItem.Material_Order_Name__c        == newItem.Material_Order_Name__c
             && oldItem.Material_Payment_Frequency__c == newItem.Material_Payment_Frequency__c
             && oldItem.Material_Payment_Terms__c     == newItem.Material_Payment_Terms__c
             && oldItem.Material_Type__c              == newItem.Material_Type__c
             && oldItem.Planned_Amount__c             == newItem.Planned_Amount__c
             && oldItem.Planned_Date__c               == newItem.Planned_Date__c
             && oldItem.Sub_Amount_Variation__c       == newItem.Sub_Amount_Variation__c
             && oldItem.Sub_Payment_Frequency__c      == newItem.Sub_Payment_Frequency__c
             && oldItem.Sub_Payment_Terms__c          == newItem.Sub_Payment_Terms__c
             && oldItem.Subcontractor_Name__c         == newItem.Subcontractor_Name__c
             && oldItem.Subcontractor_Scope__c        == newItem.Subcontractor_Scope__c
             && oldItem.Week_Start_Date__c            == newItem.Week_Start_Date__c
            ) {
                continue;
            }

            historyToInsert.add(new Cashflow_Line_Item_History__c(
                Cashflow_Line_Item__c            = recId,
               
                Line_Item_Category__c           = oldItem.Line_Item_Category__c,
                Material_Amount_Variation__c    = oldItem.Material_Amount_Variation__c,
                Material_Order_Name__c          = oldItem.Material_Order_Name__c,
                Material_Payment_Frequency__c   = oldItem.Material_Payment_Frequency__c,
                Material_Payment_Terms__c       = oldItem.Material_Payment_Terms__c,
                Material_Type__c                = oldItem.Material_Type__c,
                Planned_Amount__c               = oldItem.Planned_Amount__c,
                Planned_Date__c                 = oldItem.Planned_Date__c,
                Sub_Amount_Variation__c         = oldItem.Sub_Amount_Variation__c,
                Sub_Payment_Frequency__c        = oldItem.Sub_Payment_Frequency__c,
                Sub_Payment_Terms__c            = oldItem.Sub_Payment_Terms__c,
                Subcontractor_Name__c           = oldItem.Subcontractor_Name__c,
                Subcontractor_Scope__c          = oldItem.Subcontractor_Scope__c,
                Week_Start_Date__c              = oldItem.Week_Start_Date__c
            ));
        }

        DebugLogUtil.info(
            'Prepared {0} history entries out of {1} updated records',
            new List<Object>{ historyToInsert.size(), newMap.size() },
            TAGS
        );

        if (!historyToInsert.isEmpty()) {
            try {
                insert historyToInsert;
                DebugLogUtil.info(
                    'Successfully inserted {0} history entries',
                    new List<Object>{ historyToInsert.size() },
                    TAGS
                );
            } catch (DmlException de) {
                DebugLogUtil.error(
                    'Failed to insert {0} history entries',
                    de,
                    new List<Object>{ historyToInsert.size() },
                    TAGS
                );
            }
        } else {
            DebugLogUtil.info(
                'No field changes detected; skipping history insert',
                null,
                TAGS
            );
        }

        DebugLogUtil.exit(METHOD, TAGS);
    }

}