/**
 * @description Controller to fetch Cashflow records related to a Project.
 */
@SuppressWarnings('PMD.AvoidDeeplyNestedIfStmts, PMD.CognitiveComplexity, PMD.ExcessiveMethodLength,PMD')
public with sharing class ProjectCashflowController {

    private static final String CLASS_NAME = 'ProjectCashflowController';
    private static final List<String> LOG_TAGS = new List<String>{CLASS_NAME, 'ProjectCashflowQuery'};

    /**
     * @description Fetches all Cashflow__c records related to a given Project__c ID.
     * @param projectId The ID of the Project__c record.
     * @return List<Cashflow__c> A list of related Cashflow records.
     * @throws AuraHandledException if the projectId is blank or if a query exception occurs.
     */
    @AuraEnabled(cacheable=true)
    public static List<Cashflow__c> getCashflowsForProject(String projectId) {
        final String METHOD_NAME = 'getCashflowsForProject';
        Map<String, Object> params = new Map<String, Object>{'projectId' => projectId};
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, params, LOG_TAGS);

        if (String.isBlank(projectId)) {
            DebugLogUtil.error(METHOD_NAME + ': Project ID is required but was blank.', LOG_TAGS);
            throw new AuraHandledException('Project ID is required to fetch cashflows.');
        }

        List<Cashflow__c> cashflows = new List<Cashflow__c>();
        try {
            cashflows = [
                SELECT 
                    Id, 
                    Name, 
                    Status__c, 
                    Total_Project_Value__c, 
                    Forecast_Start_Date__c, 
                    Forecast_End_Date__c,
                    Version_Number__c,
                    Project_Start_Date__c,
                    CreatedDate
                FROM Cashflow__c 
                WHERE Project__c = :projectId
                ORDER BY CreatedDate DESC
            ];
            DebugLogUtil.info(METHOD_NAME + ': Fetched ' + cashflows.size() + ' Cashflow__c records for Project ID: ' + projectId, LOG_TAGS);
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Error fetching Cashflow__c records for Project ID: ' + projectId, e, LOG_TAGS);
            DebugLogUtil.saveLogs();
            
            throw new AuraHandledException('An error occurred while fetching cashflow records: ' + e.getMessage());
        }
        
        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
        return cashflows;
    }
}