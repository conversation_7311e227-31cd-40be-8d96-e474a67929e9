@SuppressWarnings('PMD')
public class UserProfileChangeLoggerController {

    @future
    public static void logProfileChanges(List<Id> userIds, List<Id> oldProfileIds, List<Id> newProfileIds) {
        List<User_Profile_Change_Log__c> changes = new List<User_Profile_Change_Log__c>();

        Set<Id> profileIds = new Set<Id>();
        profileIds.addAll(oldProfileIds);
        profileIds.addAll(newProfileIds);

        Map<Id, Profile> profileMap = new Map<Id, Profile>([SELECT Id, Name FROM Profile WHERE Id IN :profileIds]);
        Map<Id, User> userMap = new Map<Id, User>([SELECT Id, IsActive FROM User WHERE Id IN :userIds]);

        for (Integer i = 0; i < userIds.size(); i++) {
            User_Profile_Change_Log__c log = new User_Profile_Change_Log__c();
            log.UserId__c = userIds[i];
            log.OldProfileName__c = profileMap.get(oldProfileIds[i]).Name;
            log.NewProfileName__c = profileMap.get(newProfileIds[i]).Name;
            log.isActive__c = userMap.get(userIds[i]).IsActive;
            log.ChangeDate__c = System.now();
            changes.add(log);
        }

        if (!changes.isEmpty()) {
            insert changes;
        } else {
            System.debug('No profile changes detected, no records to insert.');
        }
    }
    
    
    @future
    public static void logUserDeactivations(List<Id> deactivatedUserIds) {
        List<User_Profile_Change_Log__c> changes = new List<User_Profile_Change_Log__c>();
        Map<Id, User> userMap = new Map<Id, User>([SELECT Id, Profile.Name FROM User WHERE Id IN :deactivatedUserIds]);

        for (Id userId : deactivatedUserIds) {
            User_Profile_Change_Log__c log = new User_Profile_Change_Log__c();
            log.UserId__c = userId;
            log.OldProfileName__c = userMap.get(userId).Profile.Name;
            log.NewProfileName__c = null; // No new profile for deactivation
            log.isActive__c = false;
            log.ChangeDate__c = System.now();
            changes.add(log);
        }

        if (!changes.isEmpty()) {
            insert changes;
        } else {
            System.debug('No deactivations detected, no records to insert.');
        }
    }
}
