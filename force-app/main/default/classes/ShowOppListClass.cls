@SuppressWarnings('PMD')
public without sharing class ShowOppListClass {

     public class PausedFlowsWrapper {
        @AuraEnabled public List<FlowInterview> interviews  { get; set; }
        @AuraEnabled public List<Paused_Flow__c> pausedFlows { get; set; }
        public PausedFlowsWrapper(List<FlowInterview> i, List<Paused_Flow__c> p) {
            this.interviews  = i;
            this.pausedFlows = p;
        }
    }
    
    // @AuraEnabled(cacheable=true)
    // public static List<FlowInterview> getPausedFlows() {
    //     Id currentUserId = UserInfo.getUserId();
    //     try {
    //         String userMode = Test.isrunningTest() ? '' : 'WITH USER_MODE';
    //         return [SELECT Id, Owner.FirstName, IsDeleted, Name, CreatedDate, CreatedBy.FirstName, 
    //                        LastModifiedDate, LastModifiedBy.FirstName, SystemModstamp, 
    //                        CurrentElement, InterviewLabel, PauseLabel, Guid, WasPausedFromScreen, 
    //                        FlowVersionViewId, InterviewStatus FROM FlowInterview WHERE InterviewStatus = 'Paused' AND OwnerId =: currentUserId];
    //     } catch (Exception e) {
    //         throw new AuraHandledException('Error fetching paused flows: ' + e.getMessage());
    //     }
    // }
    @AuraEnabled(cacheable=true)
    public static PausedFlowsWrapper getPausedFlows() {
        Id currentUserId = UserInfo.getUserId();

        // Static SOQL query — no dynamic parts
        List<FlowInterview> interviews = [
            SELECT Id, Owner.FirstName, Name, CreatedDate, CurrentElement, InterviewLabel, PauseLabel, Guid, WasPausedFromScreen, InterviewStatus
            FROM FlowInterview
            WHERE InterviewStatus = 'Paused'
            AND OwnerId = :currentUserId
        ];

        List<Paused_Flow__c> pausedFlows = [
            SELECT Id,
                Name,
                Form_State_JSON__c,
                Project__c,
                Status__c,
                User__c,
                User__r.Name,
                CreatedDate,
                CreatedById,
                LastModifiedDate,
                LastModifiedById,
                Pause_Reason__c
            FROM Paused_Flow__c
            WHERE User__c = :currentUserId
            AND Status__c = 'Paused'
        ];

        return new PausedFlowsWrapper(interviews, pausedFlows);
    }


    @AuraEnabled(cacheable=true)
    public static Map<String, Object> getActivityLogs(String recordId, Integer pageNumber, Integer pageSize, Boolean limtRec) {
        Integer offsetValue = (pageNumber - 1) * pageSize;
        String LoadUdated = 'Loan Updated';
        String baseQuery = 
        'SELECT Id, Name, CreatedDate, CreatedById, Comment_Body__c, LastModifiedDate, LastModifiedById, ' +
        'SystemModstamp, LastActivityDate, LastViewedDate, LastReferencedDate, Activity_Type__c, ' +
        'Account__c, Activity_Time__c, Contact__c, Item__c, Related_Record__c, User__r.FirstName, User__r.LastName ' +
        'FROM Activity_Logger__c ';
        
        String userMode = Test.isrunningTest() ? '' : 'WITH USER_MODE';

        String userId = UserInfo.getUserId();
        Id accountId;
        System.debug('userId: ' + userId);
        if (userId != null) {
            Id contactId = [SELECT Id, ContactId FROM User WHERE Id = :userId LIMIT 1].ContactId;
            System.debug('contactId: ' + contactId);

            if (contactId != null) {
               accountId = [SELECT AccountId FROM Contact WHERE Id = :contactId LIMIT 1].AccountId;	
            }
        }

        String whereClause = '';
        Integer totalRecords;
        if (recordId != null) {
            whereClause = 'WHERE Related_Record__c = :recordId AND Activity_Type__c != \'Loan Updated\'';
            totalRecords = [SELECT COUNT() FROM Activity_Logger__c WHERE Related_Record__c = :recordId ];
        } else {
            whereClause = 'WHERE Account__c = :accountId AND Activity_Type__c != \'Loan Updated\'';
            totalRecords = [SELECT COUNT() FROM Activity_Logger__c WHERE Account__c = :accountId];
        }
    
        String orderClause = 'ORDER BY Activity_Time__c DESC ';

        String limitOffsetClause;
        if (limtRec == true && (recordId == null || recordId != null)) {
            limitOffsetClause = 'LIMIT 5';
        } else {
            limitOffsetClause = 'LIMIT :pageSize OFFSET :offsetValue';
        }

        String query = baseQuery + whereClause + orderClause + limitOffsetClause;
        List<Activity_Logger__c> logs = Database.query(query);
        
        return new Map<String, Object>{
            'logs' => logs,
            'totalRecords' => totalRecords
        };
    }

    @AuraEnabled(cacheable=true)
    public static String getObjectType(Id recordId) {
        if (String.isEmpty(recordId)) {
            throw new IllegalArgumentException('Invalid Record ID');
        }
        return recordId.getSObjectType().getDescribe().getName();
    }

    @AuraEnabled(cacheable = true)
    public static List<OpportunityWrapper> opportunityList() {
        System.debug('opportunityList: ');
        List<OpportunityWrapper> opportunityWrappers = new List<OpportunityWrapper>();

        String userId = UserInfo.getUserId();
        System.debug('userId: ' + userId);
        if (userId != null) {
            String userMode = Test.isrunningTest() ? '' : 'WITH USER_MODE';
            Id contactId = [SELECT Id, ContactId FROM User WHERE Id = :userId LIMIT 1].ContactId;
            System.debug('contactId: ' + contactId);

            if (contactId != null) {
                Id accountId = [SELECT AccountId FROM Contact WHERE Id = :contactId LIMIT 1].AccountId;
                System.debug('accountId: ' + accountId);

                if (accountId != null) {
                    List<Opportunity> opportunities = [
                        SELECT Id, Name, Amount, Signed_App__c, Loan_Amount_Requested__c, CloseDate, StageName, Status_Update_for_Client__c, CreatedDate
                        FROM Opportunity
                        WHERE AccountId = :accountId And Status_Update_for_Client__c !=  'Declined'
                    ];
                    
                    Map<Id, Integer> projectCountMap = new Map<Id, Integer>();
                    List<AggregateResult> projectCounts = [
                        SELECT Loan_Opportunity__c, COUNT(Id) projectCount
                        FROM Project__c
                        WHERE Loan_Opportunity__c IN :opportunities 
                        GROUP BY Loan_Opportunity__c 
                    ];

                    for (AggregateResult ar : projectCounts) {
                        projectCountMap.put((Id) ar.get('Loan_Opportunity__c'), (Integer) ar.get('projectCount'));
                    }

                    Set<Id> paidOffOpportunityIds = new Set<Id>();
                    List<Project__c> projectsWithPaidOffDate = [
                        SELECT Loan_Opportunity__c, Date_Loan_Paid_Off__c, Money_Available_to_Draw_on_the_Loan__c, Current_Pay_Off_Balance__c, Loan_Principal__c
                        FROM Project__c
                        WHERE Loan_Opportunity__c IN :opportunities AND Date_Loan_Paid_Off__c != NULL 
                    ];

                    for (Project__c project : projectsWithPaidOffDate) {
                        paidOffOpportunityIds.add(project.Loan_Opportunity__c);
                    }
                    
                    List<FeedItem> feedItems = [
                        SELECT Id, ParentId, Body, (SELECT Id FROM FeedComments)
                        FROM FeedItem
                        WHERE ParentId IN :opportunities
                    ];

                    Map<Id, Integer> feedItemCountMap = new Map<Id, Integer>();
                    for (FeedItem item : feedItems) {
                        if (!String.isBlank(item.Body)) {
                            Integer msgCount = 0;
                            if (feedItemCountMap.containsKey(item.ParentId)) {
                                msgCount = feedItemCountMap.get(item.ParentId);
                            }
							msgCount = msgCount+1;
                            if(item.FeedComments != null && item.FeedComments.size() > 0) {
                                msgCount = msgCount + item.FeedComments.size();
                            }
                            feedItemCountMap.put(item.ParentId, msgCount);
                            
                        }
                    }

                    for (Opportunity opp : opportunities) {
                        Integer feedItemCount = feedItemCountMap.get(opp.Id) != null ? feedItemCountMap.get(opp.Id) : 0;
                        Integer projectCount = projectCountMap.containsKey(opp.Id) ? projectCountMap.get(opp.Id) : 0;
                        // Variable to hold aggregated totals
                        Decimal totalAvailable = 0;
                        Decimal totalPayoff = 0;
                        
                        // Only for Funded or Paid Off opportunities, aggregate child project values.
                        if (paidOffOpportunityIds.contains(opp.Id) || opp.Status_Update_for_Client__c == 'Approved') {
                            List<Project__c> projects = [
                                SELECT Money_Available_to_Draw_on_the_Loan__c, Current_Pay_Off_Balance__c, Total_Loan_Disbursements__c
                                FROM Project__c 
                                WHERE Loan_Opportunity__c = :opp.Id
                            ];
                            for (Project__c p : projects) {
                                totalAvailable += (p.Money_Available_to_Draw_on_the_Loan__c != null ? p.Money_Available_to_Draw_on_the_Loan__c : 0);
                                totalPayoff += (p.Total_Loan_Disbursements__c != null ? p.Total_Loan_Disbursements__c : 0);
                            }
                        }
                        
                        if (paidOffOpportunityIds.contains(opp.Id)) {
                            OpportunityWrapper wrapper = new OpportunityWrapper(opp, feedItemCount, projectCount, 'Paid Off Loan');
                            wrapper.totalAvailableAmount = totalAvailable;
                            wrapper.totalPayoffAmount = totalPayoff;
                            opportunityWrappers.add(wrapper);
                        } else if (opp.Status_Update_for_Client__c == 'Approved') {
                            OpportunityWrapper wrapper = new OpportunityWrapper(opp, feedItemCount, projectCount, 'Funded');
                            wrapper.totalAvailableAmount = totalAvailable;
                            wrapper.totalPayoffAmount = totalPayoff;
                            opportunityWrappers.add(wrapper);
                        } else {
                            // For non-funded, totals remain zero.
                            opportunityWrappers.add(new OpportunityWrapper(opp, feedItemCount, projectCount, 'Non-Funded'));
                        }
                    }
                }
            }
        }
        System.debug('opportunityWrappers ' + opportunityWrappers);
        return opportunityWrappers;
    }

    public class OpportunityWrapper {
        @AuraEnabled
        public Opportunity opportunity { get; set; }
        @AuraEnabled
        public Integer feedItemCount { get; set; }
        @AuraEnabled
        public Integer projectCount { get; set; }
        @AuraEnabled
        public String category { get; set; }
        // NEW: Aggregated totals instead of project list
        @AuraEnabled
        public Decimal totalAvailableAmount { get; set; }
        @AuraEnabled
        public Decimal totalPayoffAmount { get; set; }

        public OpportunityWrapper(Opportunity opp, Integer feedItemCount, Integer projectCount, String category) {
            this.opportunity = opp;
            this.feedItemCount = feedItemCount;
            this.projectCount = projectCount;
            this.category = category;
            this.totalAvailableAmount = 0;
            this.totalPayoffAmount = 0;
        }

        public OpportunityWrapper(Opportunity opp) {
            this.opportunity = opp;
            this.feedItemCount = 0;
            this.projectCount = 0;
            this.category = 'Non-Funded';
            this.totalAvailableAmount = 0;
            this.totalPayoffAmount = 0;
        }
    }
}