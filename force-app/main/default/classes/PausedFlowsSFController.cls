@SuppressWarnings('PMD')
public without sharing class PausedFlowsSFController {

    public class PausedItem implements Comparable {
        @AuraEnabled public String Id { get; set; }
        @AuraEnabled public String PauseLabel { get; set; }            // "Paused Reason" column
        @AuraEnabled public String ExtractedInterviewLabel { get; set; } // "Flow Name" column
        @AuraEnabled public String OwnerName { get; set; }
        @AuraEnabled public String CreateDD { get; set; }              // Formatted Date String
        @AuraEnabled public Datetime OriginalCreatedDate { get; set; } // For sorting
        @AuraEnabled public String ItemType { get; set; }              // 'FlowInterview' or 'Paused_Flow__c'

        // Constructor for FlowInterview
        public PausedItem(FlowInterview fi, Map<Id, User> userCache) {
            this.Id = fi.Id;
            this.PauseLabel = fi.PauseLabel;

            if (fi.InterviewLabel != null) {
                Matcher m = Pattern.compile('^[^\\d]+').matcher(fi.InterviewLabel);
                this.ExtractedInterviewLabel = m.find() ? m.group(0).trim() : fi.InterviewLabel;
            } else {
                this.ExtractedInterviewLabel = fi.Name != null ? fi.Name : 'Unnamed Flow'; // Fallback
            }
            
            // Set OwnerName using Owner.FirstName if available, otherwise CreatedBy.FirstName
            if (fi.Owner != null && fi.Owner.FirstName != null) {
                this.OwnerName = fi.Owner.FirstName;
            } else if (fi.CreatedBy != null && fi.CreatedBy.FirstName != null) {
                 this.OwnerName = fi.CreatedBy.FirstName;
            } else if (fi.OwnerId != null && userCache.containsKey(fi.OwnerId) && userCache.get(fi.OwnerId).FirstName != null) {
                this.OwnerName = userCache.get(fi.OwnerId).FirstName;
            } else if (fi.CreatedById != null && userCache.containsKey(fi.CreatedById) && userCache.get(fi.CreatedById).FirstName != null) {
                this.OwnerName = userCache.get(fi.CreatedById).FirstName;
            }
            else {
                this.OwnerName = 'Unknown';
            }

            this.CreateDD = formatDate(fi.CreatedDate);
            this.OriginalCreatedDate = fi.CreatedDate;
            this.ItemType = 'FlowInterview';
        }

        // Constructor for Paused_Flow__c
        public PausedItem(Paused_Flow__c pf) {
            this.Id = pf.Id;
            // "Paused Reason" column will show the Name of the Paused_Flow__c record
            this.PauseLabel = pf.Pause_Reason__c;
            // "Flow Name" column will be 'Disbursement Request Form' for these custom paused flows.
            // This helps them pass the LWC filter and aligns with community component logic.
            this.ExtractedInterviewLabel = 'Disbursement Request Form'; 
            this.OwnerName = (pf.User__r != null && pf.User__r.Name != null) ? pf.User__r.Name : 'Unknown';
            this.CreateDD = formatDate(pf.CreatedDate);
            this.OriginalCreatedDate = pf.CreatedDate;
            this.ItemType = 'Paused_Flow__c';
        }

        private String formatDate(Datetime dt) {
            if (dt == null) return '';
            // Format: YYYY-MM-DD
            return dt.year() + '-' + String.valueOf(dt.month()).leftPad(2, '0') + '-' + String.valueOf(dt.day()).leftPad(2, '0');
        }

        // For sorting by OriginalCreatedDate descending
        public Integer compareTo(Object compareTo) {
            PausedItem compareToItem = (PausedItem)compareTo;
            if (this.OriginalCreatedDate == null && compareToItem.OriginalCreatedDate == null) return 0;
            if (this.OriginalCreatedDate == null) return 1; // Nulls last
            if (compareToItem.OriginalCreatedDate == null) return -1; // Nulls last
            
            if (this.OriginalCreatedDate < compareToItem.OriginalCreatedDate) return 1;
            if (this.OriginalCreatedDate > compareToItem.OriginalCreatedDate) return -1;
            return 0;
        }
    }

    @AuraEnabled(cacheable=true)
    public static List<PausedItem> getPausedFlows(Id accountId) {
        List<PausedItem> combinedList = new List<PausedItem>();
        Set<Id> userIds = new Set<Id>();
        Map<Id, User> userCache = new Map<Id, User>();

        // Get users associated with the account
        for (User u : [SELECT Id, Name, FirstName FROM User WHERE AccountId = :accountId]) {
            userIds.add(u.Id);
            userCache.put(u.Id, u);
        }

        if (userIds.isEmpty()) {
            return combinedList; // No users, so no paused flows for this account
        }

        // 1. Get FlowInterviews
        // Added CreatedBy.FirstName to ensure OwnerName can be populated
        List<FlowInterview> flowInterviews = [
            SELECT Id, Owner.FirstName, OwnerId, Name, CreatedDate, CreatedById, CreatedBy.FirstName,
                   InterviewLabel, PauseLabel, InterviewStatus
            FROM FlowInterview
            WHERE (CreatedById IN :userIds OR OwnerId IN :userIds) // Consider flows created by or owned by these users
            AND InterviewStatus = 'Paused'
        ];
        for (FlowInterview fi : flowInterviews) {
            combinedList.add(new PausedItem(fi, userCache));
        }

        // 2. Get Paused_Flow__c records
        List<Paused_Flow__c> customPausedFlows = [
            SELECT Id, Name, CreatedDate, User__c, User__r.Name, Status__c, Pause_Reason__c
            FROM Paused_Flow__c
            WHERE User__c IN :userIds
            AND Status__c = 'Paused' // Ensure this is the correct status for paused items
        ];
        for (Paused_Flow__c pf : customPausedFlows) {
            combinedList.add(new PausedItem(pf));
        }

        // 3. Sort the combined list by creation date (descending)
        combinedList.sort();

        return combinedList;
    }

    @AuraEnabled
    public static String discardFlowInterview(String flowInterviewId) {
        try {
            // Ensure the ID is for a FlowInterview to prevent accidental deletion of other record types
            // if this method were ever to be misused, though the client side will control this.
            
            if(Id.valueOf(flowInterviewId).getSObjectType().getDescribe().getName().equals('FlowInterview')) {
                FlowInterview fiToDelete = [SELECT Id FROM FlowInterview WHERE Id = :flowInterviewId LIMIT 1];
                if (fiToDelete != null) {
                    delete fiToDelete;
                    return 'Success: Flow Interview discarded.';
                }
            } else {
                Paused_Flow__c fiToDelete = [SELECT Id FROM Paused_Flow__c WHERE Id = :flowInterviewId LIMIT 1];
                if (fiToDelete != null) {
                    delete fiToDelete;
                    return 'Success: Flow Interview discarded.';
                }
            }
            return '';
        } catch (Exception e) {
            System.debug('Error discarding Flow Interview: ' + e.getMessage() + ' Stack: ' + e.getStackTraceString());
            // Throw AuraHandledException to send a user-friendly error message to the LWC
            throw new AuraHandledException('Error discarding submission: ' + e.getMessage());
        }
    }
}