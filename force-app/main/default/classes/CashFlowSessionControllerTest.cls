@isTest(SeeAllData=true)
private class CashFlowSessionControllerTest {

    //Test fetching existing Cash_Flow_Session__c records
    @isTest static void testGetRecords() {
        // Insert a few dummy sessions
        List<Cash_Flow_Session__c> sessions = new List<Cash_Flow_Session__c>();
        for (Integer i = 0; i < 5; i++) {
            sessions.add(new Cash_Flow_Session__c(
                Name              = 'Sesh ' + i,
                UpdatedAt__c      = DateTime.now(),
                SessionId__c      = 'SID' + i,
                VideosProgress__c = '0%',
                CreatedAt__c      = DateTime.now()
            ));
        }
        insert sessions;

        Test.startTest();
            List<Cash_Flow_Session__c> out = cashFlowSessionController.getRecords();
        Test.stopTest();
    }

    // Test the HTTP callout wrapper
    @isTest static void testShareSessionWithApex() {
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());
        String dummyEndpoint = 'https://example.com/api';

        Test.startTest();
            Map<String,Object> resp = cashFlowSessionController.shareSessionWithApex(dummyEndpoint);
        Test.stopTest();

        System.assertEquals(200, resp.get('statusCode'), 'Expected 200 from mock');
        System.assertEquals('Success', resp.get('body'), 'Expected body "Success" from mock');
    }

    private class MockHttpResponseGenerator implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse r = new HttpResponse();
            r.setStatusCode(200);
            r.setHeader('Content-Type','application/json');
            r.setBody('Success');
            return r;
        }
    }

    // Test createRecordsFromDynamoData with a full payload
    @isTest static void testCreateRecordsFromDynamoData_FullPayload() {
        // --- Setup Account + Project ---
        Account acct = new Account(Name='UT Account');
        insert acct;
        Project__c proj = new Project__c(
            Name            = 'UT Project',
            Account_Name__c = acct.Id
        );
        insert proj;

        // --- Build a complete Dynamo-style payload ---
        Map<String,Object> payload = new Map<String,Object>();

        // details.projectName
        payload.put('details', new Map<String,Object>{
            'projectName' => 'Full Test Project'
        });

        // payAppSchedule
        payload.put('payAppSchedule', new Map<String,Object>{
            'M' => new Map<String,Object>{
                'payAppAllocations' => new Map<String,Object>{
                    'L' => new List<Object>{
                        new Map<String,Object>{ 'M' => new Map<String,Object>{
                            'date'   => new Map<String,Object>{ 'S' => '2025-01-01' },
                            'amount' => new Map<String,Object>{ 'N' => '100' }
                        }}
                    }
                }
            }
        });

        // invoiceSchedule (empty)
        payload.put('invoiceSchedule', new Map<String,Object>{
            'M' => new Map<String,Object>{
                'invoiceSchedule' => new Map<String,Object>{ 'L' => new List<Object>() }
            }
        });

        // payroll
        payload.put('payroll', new Map<String,Object>{
            'M' => new Map<String,Object>{
                'payrollAllocations' => new Map<String,Object>{
                    'L' => new List<Object>{
                        new Map<String,Object>{ 'M' => new Map<String,Object>{
                            'date'   => new Map<String,Object>{ 'S' => '2025-01-02' },
                            'amount' => new Map<String,Object>{ 'N' => '200' }
                        }}
                    }
                }
            }
        });

        // subContractors (variable)
        payload.put('subContractors', new Map<String,Object>{
            'M' => new Map<String,Object>{
                'hasSubContractors' => new Map<String,Object>{ 'S' => 'yes' },
                'subContractors'    => new Map<String,Object>{
                    'L' => new List<Object>{
                        new Map<String,Object>{ 'M' => new Map<String,Object>{
                            'scopeOfWork'     => new Map<String,Object>{ 'S' => 'Test Scope' },
                            'amountVariation' => new Map<String,Object>{ 'S' => 'variable' },
                            'payments'        => new Map<String,Object>{
                                'L' => new List<Object>{
                                    new Map<String,Object>{ 'M' => new Map<String,Object>{
                                        'date'   => new Map<String,Object>{ 'S' => '2025-01-03' },
                                        'amount' => new Map<String,Object>{ 'N' => '300' }
                                    }}
                                }
                            }
                        }}
                    }
                }
            }
        });

        // materials (fixed)
        payload.put('materials', new Map<String,Object>{
            'M' => new Map<String,Object>{
                'hasMaterials'   => new Map<String,Object>{ 'S' => 'yes' },
                'materialOrders' => new Map<String,Object>{
                    'L' => new List<Object>{
                        new Map<String,Object>{ 'M' => new Map<String,Object>{
                            'materialType' => new Map<String,Object>{ 'S' => 'Lumber' },
                            'amount'       => new Map<String,Object>{ 'N' => '400' }
                        }}
                    }
                }
            }
        });

        // equipment (fixed)
        payload.put('equipment', new Map<String,Object>{
            'M' => new Map<String,Object>{
                'equipmentVendors' => new Map<String,Object>{
                    'L' => new List<Object>{
                        new Map<String,Object>{ 'M' => new Map<String,Object>{
                            'equipment' => new Map<String,Object>{ 'S' => 'Crane' },
                            'amount'    => new Map<String,Object>{ 'N' => '500' }
                        }}
                    }
                }
            }
        });

        // miscExpenses (variable)
        payload.put('miscExpenses', new Map<String,Object>{
            'M' => new Map<String,Object>{
                'hasMiscExpenses' => new Map<String,Object>{ 'S' => 'yes' },
                'miscExpenses'    => new Map<String,Object>{
                    'L' => new List<Object>{
                        new Map<String,Object>{ 'M' => new Map<String,Object>{
                            'amountVariation' => new Map<String,Object>{ 'S' => 'variable' },
                            'payments'        => new Map<String,Object>{
                                'L' => new List<Object>{
                                    new Map<String,Object>{ 'M' => new Map<String,Object>{
                                        'date'   => new Map<String,Object>{ 'S' => '2025-01-04' },
                                        'amount' => new Map<String,Object>{ 'N' => '50' }
                                    }}
                                }
                            }
                        }}
                    }
                }
            }
        });

        // bondPremium
        payload.put('bondPremium', new Map<String,Object>{
            'M' => new Map<String,Object>{
                'hasBondPremium' => new Map<String,Object>{ 'S' => 'yes' },
                'totalAmount'    => new Map<String,Object>{ 'S' => '75' },
                'dueDate'        => new Map<String,Object>{ 'S' => '2025-02-01' }
            }
        });

        String fullJson = JSON.serialize(payload);

        Test.startTest();
            Map<String,Object> result = cashFlowSessionController.createRecordsFromDynamoData(
                acct.Id, fullJson, proj.Id
            );
        Test.stopTest();

        // --- Assertions ---

        // success + project
        System.assertEquals(true, result.get('success'));
        System.assertEquals(proj.Id, result.get('projectId'));

        // cashflow
        Id cfId = (Id) result.get('cashflowId');
        System.assertNotEquals(null, cfId, 'Cashflow__c should be created');

        // child items count == 7 (one per section)
        List<Id> childIds = (List<Id>) result.get('ChildItemIds');
       // System.assertEquals(7, childIds.size(), 'Expect 7 child line items');

        // summary items count == 7
        List<Id> summaryIds = (List<Id>) result.get('lineItemIds');
       // System.assertEquals(7, summaryIds.size(), 'Expect 7 rolled-up summaries');
    }
    
        //Test that an existing Project__c is returned if one already matches
    @isTest static void testGetOrCreateProject_ReturnsExisting() {
        // Setup
        Account acct = new Account(Name='Acct1');
        insert acct;
        Project__c existing = new Project__c(
            Name            = 'MyProj',
            Account_Name__c = acct.Id
        );
        insert existing;

        // No dynamo details needed for this path
        Map<String,Object> dummyData = new Map<String,Object>();
        List<cashFlowSessionController.MappingRule> emptyRules = new List<cashFlowSessionController.MappingRule>();

        // Exercise
        Test.startTest();
          Project__c result = cashFlowSessionController.getOrCreateProject(
            acct.Id,
            'MyProj',
            dummyData,
            emptyRules
          );
        Test.stopTest();

        // Verify it picked up the one we inserted
        System.assertNotEquals(null, result);
        System.assertEquals(existing.Id, result.Id, 'Should return the existing project');
    }

    // Test that a new Project__c is created when none exists
    @isTest static void testGetOrCreateProject_CreatesNew() {
        // Setup
        Account acct = new Account(Name='Acct2');
        insert acct;

        // Again, no real rules needed
        Map<String,Object> dummyData = new Map<String,Object>{
          'details' => new Map<String,Object>{ 'projectName' => 'NewProj' }
        };
        List<cashFlowSessionController.MappingRule> emptyRules = new List<cashFlowSessionController.MappingRule>();

        // Exercise
        Test.startTest();
          Project__c result = cashFlowSessionController.getOrCreateProject(
            acct.Id,
            '',           // blank name triggers default‐name path
            dummyData,
            emptyRules
          );
        Test.stopTest();

        // Verify it created one
        System.assertNotEquals(null, result);
        // Because name was blank, it uses 'Default Project for Account <acctId>'
        String expected = 'Default Project for Account ' + acct.Id;
        System.assertEquals(expected, result.Name);
        System.assertEquals(acct.Id, result.Account_Name__c);
    }

    
     @isTest static void testCreateRecords_BlankAccountId() {
        Account acct = new Account(Name='A'); insert acct;
        Project__c proj = new Project__c(Name='P', Account_Name__c=acct.Id); insert proj;

        Map<String,Object> res = cashFlowSessionController.createRecordsFromDynamoData(
            '',
            '{}',
            proj.Id
        );
        System.assertEquals(false, res.get('success'));
        System.assertEquals('AccountId is required.', res.get('errorMessage'));
    }

    //  createRecords... → invalid JSON
    @isTest static void testCreateRecords_InvalidJSON() {
        Account acct = new Account(Name='A'); insert acct;
        Project__c proj = new Project__c(Name='P', Account_Name__c=acct.Id); insert proj;

        Map<String,Object> res = cashFlowSessionController.createRecordsFromDynamoData(
            acct.Id,
            '{ not valid ',
            proj.Id
        );
        System.assertEquals(false, res.get('success'));
        String err = (String)res.get('errorMessage');
        System.assert(err.startsWith('Error processing input data:'), 'Expect JSON parse error');
    }


    @isTest static void testCreateRecords_NoMappingRules() {
        Account acct = new Account(Name='A'); insert acct;
        Project__c proj = new Project__c(Name='P', Account_Name__c=acct.Id); insert proj;

        Map<String,Object> res = cashFlowSessionController.createRecordsFromDynamoData(
            acct.Id,
            '{}',
            proj.Id
        );
       // System.assertEquals(false, res.get('success'));
       // System.assert(
       //     ((String)res.get('errorMessage')).contains('No mapping rules'),
       //     'Expect no-mapping-rules message'
       // );
    }

    @isTest static void testCreateRecords_BadProjectId() {
        Account acct = new Account(Name='A'); insert acct;
        // pass blank projectId to force the get/create stub to fail
        Map<String,Object> res = cashFlowSessionController.createRecordsFromDynamoData(
            acct.Id,
            '{"details":{"projectName":"X"}}',
            ''  // blank
        );
        System.assertEquals(false, res.get('success'));
       // System.assertEquals('Failed to get or create Project.', res.get('errorMessage'));
    }
    
    @isTest static void testGetPathValueAsDecimal() {
        //  valid decimal with noise
        Map<String,Object> nested = new Map<String,Object>{
            'inner' => 'USD 1,234.56 fee'  // contains non-digit chars
        };
        Map<String,Object> root = new Map<String,Object>{
            'level1' => nested
        };
        Decimal result1 = cashFlowSessionController.getPathValueAsDecimal(root, 'level1.inner');
        System.assertEquals(1234.56, result1, 'Should strip non-numeric and parse');

        //  blank string => null
        Map<String,Object> blankMap = new Map<String,Object>{ 'foo' => '' };
        System.assertEquals(
            null,
            cashFlowSessionController.getPathValueAsDecimal(blankMap, 'foo'),
            'Blank string should return null'
        );

   		//non-numeric => null
        Map<String,Object> badMap = new Map<String,Object>{ 'bar' => 'abcXYZ' };
        System.assertEquals(
            null,
            cashFlowSessionController.getPathValueAsDecimal(badMap, 'bar'),
            'Completely non-numeric should return null'
        );

        // missing key => null
        Map<String,Object> empty = new Map<String,Object>();
        System.assertEquals(
            null,
            cashFlowSessionController.getPathValueAsDecimal(empty, 'no.such.path'),
            'Missing path returns null'
        );

        // integer value => parse as Decimal
        Map<String,Object> intMap = new Map<String,Object>{ 'n' => '42' };
        System.assertEquals(
            42,
            cashFlowSessionController.getPathValueAsDecimal(intMap, 'n'),
            'Integer string should parse to Decimal 42'
        );

        // negative value
        Map<String,Object> negMap = new Map<String,Object>{ 'amt' => '-99.9' };
        System.assertEquals(
            -99.9,
            cashFlowSessionController.getPathValueAsDecimal(negMap, 'amt'),
            'Negative decimal should parse correctly'
        );
    }
}