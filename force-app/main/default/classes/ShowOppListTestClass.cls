@isTest
public class ShowOppListTestClass {
    @testSetup
    static void setupTestData() {
        // -- existing setup, unchanged --
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        Contact testContact = new Contact(LastName = 'Test', AccountId = testAccount.Id);
        insert testContact;

        Profile profile = [SELECT Id FROM Profile WHERE Name = 'Customer Community User' LIMIT 1];
        User testUser = new User(
            ProfileId               = profile.Id,
            EmailEncodingKey        = 'ISO-8859-1',
            LanguageLocaleKey       = 'en_US',
            TimeZoneSidKey          = 'America/New_York',
            LocaleSidKey            = 'en_US',
            FirstName               = 'Test',
            LastName                = 'User',
            Username                = '<EMAIL>',
            CommunityNickname       = 'testUser123',
            Alias                   = 'tusr',
            Email                   = '<EMAIL>',
            IsActive                = true,
            ContactId               = testContact.Id
        );
        insert testUser;

        Opportunity testOpportunity = new Opportunity(
            Name        = 'Test Opportunity',
            StageName   = 'Prospecting',
            CloseDate   = Date.today().addDays(30),
            AccountId   = testAccount.Id
        );
        insert testOpportunity;

        Project__c testProject = new Project__c(
            Name                            = 'Test Project',
            Loan_Opportunity__c             = testOpportunity.Id,
            Email_for_Servicing_Updates__c = '<EMAIL>',
            Date_Loan_Paid_Off__c           = Date.newInstance(2024, 11, 15)
        );
        insert testProject;
    }

    @isTest
    static void testOpportunityListAndActivityLogs() {
        // Prepare a FeedItem under the test opportunity
        Opportunity opp = [SELECT Id FROM Opportunity WHERE Name = 'Test Opportunity' LIMIT 1];
        FeedItem feedItem = new FeedItem(
            Body       = 'Test feed item body',
            ParentId   = opp.Id,
            Type       = 'TextPost',
            Visibility = 'AllUsers'
        );
        insert feedItem;

        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        System.runAs(testUser) {
            // opportunityList
            List<ShowOppListClass.OpportunityWrapper> wrappers = ShowOppListClass.opportunityList();
            System.assert(!wrappers.isEmpty(), 'Should return at least one wrapper');

            // getActivityLogs with recordId
            Map<String, Object> resultWithId = ShowOppListClass.getActivityLogs(opp.Id, 1, 10, true);
            System.assert(resultWithId.containsKey('logs'), 'Should include logs');
            System.assert(resultWithId.containsKey('totalRecords'), 'Should include totalRecords');

            // getActivityLogs without recordId
            Map<String, Object> resultNoId = ShowOppListClass.getActivityLogs(null, 1, 10, true);
            System.assert(resultNoId.containsKey('logs'), 'Should include logs when no recordId');
        }
    }

    @isTest
    static void testGetPausedFlows() {
        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        System.runAs(testUser) {
            // Now returns a wrapper instead of List<FlowInterview>
            ShowOppListClass.PausedFlowsWrapper wrapper = ShowOppListClass.getPausedFlows();
            System.assertNotEquals(null, wrapper, 'Wrapper must be non-null');
            System.assertNotEquals(null, wrapper.interviews, 'Interviews list must be instantiated');
            System.assertNotEquals(null, wrapper.pausedFlows, 'PausedFlows list must be instantiated');
        }
    }

    @isTest
    static void testGetObjectType() {
        Account testAccount = new Account(Name = 'Another Test');
        insert testAccount;

        Test.startTest();
        String objName = ShowOppListClass.getObjectType(testAccount.Id);
        Test.stopTest();
        System.assertEquals('Account', objName, 'Should return correct sObject name');

        // invalid case
        try {
            ShowOppListClass.getObjectType(null);
            System.assert(false, 'Expected exception on null Id');
        } catch (IllegalArgumentException e) {
            System.assertEquals('Invalid Record ID', e.getMessage());
        }
    }
}