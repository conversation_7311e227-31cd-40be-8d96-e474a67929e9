/**
 * @description Service class for retrieving and managing MF Dynamo to SF Mapping metadata.
 * This class provides methods to retrieve mapping rules from the MF_Dynamo_SF_Mapping__mdt
 * custom metadata type and convert them to the format expected by the cashFlowSessionController.
 * <AUTHOR>
 * @date 2025
 */
@SuppressWarnings('PMD.AvoidDeeplyNestedIfStmts, PMD.CognitiveComplexity, PMD.ExcessiveMethodLength,PMD')
public with sharing class MFDynamoMappingService {
    
    /**
     * @description Retrieves all active mapping rules from the MF_Dynamo_SF_Mapping__mdt custom metadata type
     * and converts them to the format expected by the cashFlowSessionController.
     * @return String JSON string containing the mapping rules in the expected format
     */
    @AuraEnabled(cacheable=true)
    public static String getMappingRulesAsJson() {
        List<cashFlowSessionController.MappingRule> mappingRules = new List<cashFlowSessionController.MappingRule>();
        
        // Query all active mapping rules
        List<MF_Dynamo_SF_Mapping__mdt> mappingMetadata = [
            SELECT 
                Dynamo_Path__c, 
                Target_Salesforce_Object_API_Name__c, 
                Target_Salesforce_Field_API_Name__c, 
                UI_Field_Label__c, 
                Intake_Context_Type__c, 
                Intake_Form_Section__c,
                Processing_Order__c
            FROM MF_Dynamo_SF_Mapping__mdt
            WHERE Is_Active__c = true
            ORDER BY Processing_Order__c ASC NULLS LAST
        ];
        
        // Convert metadata records to MappingRule objects
        for (MF_Dynamo_SF_Mapping__mdt metadata : mappingMetadata) {
            cashFlowSessionController.MappingRule rule = new cashFlowSessionController.MappingRule();
            rule.dynamoDbId = metadata.Dynamo_Path__c;
            rule.salesforceObject = metadata.Target_Salesforce_Object_API_Name__c;
            rule.salesforceFieldApiName = metadata.Target_Salesforce_Field_API_Name__c;
            rule.fieldName = metadata.UI_Field_Label__c;
            rule.intakeType = metadata.Intake_Context_Type__c;
            rule.intakeFormPageSection = metadata.Intake_Form_Section__c;
            
            mappingRules.add(rule);
        }
        
        // Convert to JSON string
        return JSON.serialize(mappingRules);
    }
    
    /**
     * @description Retrieves all active mapping rules for a specific Salesforce object
     * @param objectApiName The API name of the Salesforce object to retrieve mapping rules for
     * @return List<MF_Dynamo_SF_Mapping__mdt> List of mapping metadata records
     */
    public static List<MF_Dynamo_SF_Mapping__mdt> getMappingRulesForObject(String objectApiName) {
        return [
            SELECT 
                Dynamo_Path__c, 
                Target_Salesforce_Object_API_Name__c, 
                Target_Salesforce_Field_API_Name__c, 
                UI_Field_Label__c, 
                Intake_Context_Type__c, 
                Intake_Form_Section__c,
                Processing_Order__c,
                Notes__c
            FROM MF_Dynamo_SF_Mapping__mdt
            WHERE Is_Active__c = true
            AND Target_Salesforce_Object_API_Name__c = :objectApiName
            ORDER BY Processing_Order__c ASC NULLS LAST
        ];
    }
    
    /**
     * @description Retrieves all active mapping rules for a specific intake type
     * @param intakeType The intake type to retrieve mapping rules for (e.g., "Construction", "Purchase Order")
     * @return List<MF_Dynamo_SF_Mapping__mdt> List of mapping metadata records
     */
    public static List<MF_Dynamo_SF_Mapping__mdt> getMappingRulesForIntakeType(String intakeType) {
        String searchPattern = '%' + intakeType + '%';
        List<MF_Dynamo_SF_Mapping__mdt> results = [SELECT 
                Dynamo_Path__c, 
                Target_Salesforce_Object_API_Name__c, 
                Target_Salesforce_Field_API_Name__c, 
                UI_Field_Label__c, 
                Intake_Context_Type__c, 
                Intake_Form_Section__c,
                Processing_Order__c,
                Notes__c
            FROM MF_Dynamo_SF_Mapping__mdt
            WHERE Is_Active__c = true
            AND Intake_Context_Type__c LIKE :searchPattern 
                                                   ORDER BY Processing_Order__c ASC NULLS LAST];
        results.addAll(
  [SELECT Dynamo_Path__c, 
                Target_Salesforce_Object_API_Name__c, 
                Target_Salesforce_Field_API_Name__c, 
                UI_Field_Label__c, 
                Intake_Context_Type__c, 
                Intake_Form_Section__c,
                Processing_Order__c,
                Notes__c
            FROM MF_Dynamo_SF_Mapping__mdt
            WHERE Is_Active__c = true
            AND  Intake_Context_Type__c = null
            ORDER BY Processing_Order__c ASC NULLS LAST]);
        return results;
    }
}