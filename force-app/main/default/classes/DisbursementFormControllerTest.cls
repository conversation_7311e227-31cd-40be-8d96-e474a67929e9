@SuppressWarnings('PMD')
@isTest
private class DisbursementFormControllerTest {

    @TestSetup
    static void makeData(){
        // Common test data
        Account acc = new Account(Name='Test Account Inc.');
        insert acc;

        Contact con = new Contact(AccountId=acc.Id, LastName='TestContactPerson');
        insert con;

        Opportunity opp = new Opportunity(
            Name='Test Loan Opportunity 123',
            AccountId=acc.Id,
            StageName='Prospecting',
            CloseDate=Date.today().addMonths(1)
        );
        insert opp;

        Project__c proj = new Project__c(
            Name='Test Project Alpha',
            Account_Name__c=acc.Id,
            Loan_Opportunity__c=opp.Id
        );
        insert proj;
    }

    private static Id createTestContentDocument(String title, String fileExtension, String body) {
        ContentVersion cv = new ContentVersion(
            Title = title,
            PathOnClient = title + '.' + fileExtension,
            VersionData = Blob.valueOf(String.isNotBlank(body) ? body : 'Test file content'),
            IsMajorVersion = true
        );
        insert cv;
        return [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id].ContentDocumentId;
    }

    private static String buildRequestWrapperJSON(
        Id projectId, Id contactId, String loanName, String accountName,
        Id selectedPayeeId, List<Id> mainDocIds, List<Id> reqItemDocIds,
        Boolean hasSignature, Decimal totalAmountVal, Id loanOppId
    ) {
        DisbursementFormController.RequestWrapper wrapper = new DisbursementFormController.RequestWrapper();

        wrapper.initialDetails = new DisbursementFormController.InitialDetailsWrapper();
        wrapper.initialDetails.projectId = projectId;
        wrapper.initialDetails.contactId = contactId;
        wrapper.initialDetails.loanName = loanName;
        wrapper.initialDetails.loanId = loanOppId;
        wrapper.initialDetails.accountName = accountName;
        wrapper.initialDetails.contactName = 'Test UserSubmitter';
        wrapper.initialDetails.contactTitle = 'Project Manager';
        wrapper.initialDetails.effectiveDateofLoan = Date.today().addDays(-35);
        wrapper.initialDetails.gcOwnerNameForPerjury = accountName;
        wrapper.initialDetails.projectNameForPerjury = 'Test Project Perjury Name';

        wrapper.formDetails = new DisbursementFormController.FormDetailsWrapper();
        wrapper.formDetails.selectedPayeeId = selectedPayeeId;
        wrapper.formDetails.expenseType = 'Subcontract Labor';
        wrapper.formDetails.payeeName = 'Global Payee Corp';
        wrapper.formDetails.payeeFirstName = 'John';
        wrapper.formDetails.payeeLastName = 'PayeeDoe';
        wrapper.formDetails.payeeContactEmail = '<EMAIL>';
        wrapper.formDetails.paymentMethod = 'Wire';
        wrapper.formDetails.payeeStreet = '456 Payee Ave';
        wrapper.formDetails.payeeCity = 'Payeville';
        wrapper.formDetails.payeeState = 'PV';
        wrapper.formDetails.payeeZip = '67890';
        wrapper.formDetails.payeeCountry = 'USA';
        wrapper.formDetails.mailCheckTo = (wrapper.formDetails.paymentMethod == 'Check' ? '<EMAIL>' : null);
        wrapper.formDetails.bankName = 'Global Test Bank';
        wrapper.formDetails.bankAccountName = 'Global Payee Corp Account';
        wrapper.formDetails.bankRoutingNumber = '*********';
        wrapper.formDetails.bankAccountNumber = '*********';
        wrapper.formDetails.payeePhone = '************';
        wrapper.formDetails.yourName = 'Test Form Submitter';
        wrapper.formDetails.requesterEmail = '<EMAIL>';
        wrapper.formDetails.additionalComments = 'These are test comments for the form.';
        wrapper.formDetails.iAgreePerjury = true;
        if (hasSignature) {
            wrapper.formDetails.clientSignature = 
              'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
        } else {
            wrapper.formDetails.clientSignature = null;
        }

        wrapper.mainUploadedDocumentIds = mainDocIds != null ? mainDocIds : new List<Id>();

        wrapper.requestItemsJSON = new List<DisbursementFormController.RequestItemWrapper>();
        DisbursementFormController.RequestItemWrapper item1 = new DisbursementFormController.RequestItemWrapper();
        item1.Name = 'Service Item Alpha';
        item1.descriptionWork = 'Description for Alpha service';
        item1.invoice = 'INV-ALPHA-001';
        item1.invoiceAmount = 150.75;
        item1.invoiceDate = Date.today().addDays(-12);
        item1.invoiceDueDate = Date.today().addDays(18);
        item1.contentDocumentIds = reqItemDocIds != null ? reqItemDocIds : new List<Id>();
        wrapper.requestItemsJSON.add(item1);

        DisbursementFormController.RequestItemWrapper item2 = new DisbursementFormController.RequestItemWrapper();
        item2.Name = 'Material Item Beta (No Docs)';
        item2.descriptionWork = 'Beta materials supplied';
        item2.invoice = 'INV-BETA-002';
        item2.invoiceAmount = 250.00;
        item2.invoiceDate = Date.today().addDays(-7);
        item2.invoiceDueDate = Date.today().addDays(23);
        item2.contentDocumentIds = new List<Id>();
        wrapper.requestItemsJSON.add(item2);

        wrapper.totalAmount = totalAmountVal;

        return JSON.serialize(wrapper);
    }

    @isTest
    static void testGetDisbursementPicklistValues() {
        Test.startTest();
        Map<String, List<String>> picklistValues = DisbursementFormController.getDisbursementPicklistValues();
        Test.stopTest();

        System.assertNotEquals(null, picklistValues);
        System.assert(picklistValues.containsKey('expenseTypes'));
        System.assert(!picklistValues.get('expenseTypes').isEmpty(),
            'Should have at least one expense type defined in the org.');
    }

    @isTest
    static void testSubmitDisbursementRequest_NewPayee_Full() {
        Project__c proj = [SELECT Id, Loan_Opportunity__r.Name, Account_Name__r.Name, Loan_Opportunity__c 
                           FROM Project__c WHERE Name = 'Test Project Alpha' LIMIT 1];
        Contact con = [SELECT Id FROM Contact WHERE LastName='TestContactPerson' LIMIT 1];

        Id mainDoc1Id = createTestContentDocument('MainDocTest1','txt','Main document content Test 1');
        Id mainDoc2Id = createTestContentDocument('MainDocTest2','pdf','Main document content Test 2');
        Id itemDoc1Id = createTestContentDocument('ItemDocTest1','png','Item document content Test 1');
        
        List<Id> mainDocs = new List<Id>{mainDoc1Id, mainDoc2Id};
        List<Id> itemDocs = new List<Id>{itemDoc1Id};

        String requestJSON = buildRequestWrapperJSON(
            proj.Id, con.Id, proj.Loan_Opportunity__r.Name, proj.Account_Name__r.Name,
            null, mainDocs, itemDocs, true, 400.75, proj.Loan_Opportunity__c
        );

        Test.startTest();
        Id disbReqId = DisbursementFormController.submitDisbursementRequest(requestJSON);
        Test.stopTest();

        System.assertNotEquals(null, disbReqId);

        Disbursement_Request__c dr = [
            SELECT Id, Status__c, Payee_Name__c, Signature_Picture__c
            FROM Disbursement_Request__c WHERE Id = :disbReqId
        ];
        System.assertEquals('New', dr.Status__c);
        System.assertEquals('Global Payee Corp', dr.Payee_Name__c);
        System.assert(!String.isBlank(dr.Signature_Picture__c));
    }

    // ... other submitDisbursementRequest tests unchanged ...

    @isTest
    static void testGetPausedFormState_Success() {
        Project__c proj = [SELECT Id FROM Project__c WHERE Name = 'Test Project Alpha' LIMIT 1];
        Paused_Flow__c pf = new Paused_Flow__c(
            Project__c = proj.Id,
            Form_State_JSON__c = '{"testKey":"testPausedValue"}',
            Status__c = 'Paused',
            User__c = UserInfo.getUserId()
        );
        insert pf;

        Test.startTest();
        String formState = DisbursementFormController.getPausedFormState(pf.Id);
        Test.stopTest();

        System.assertEquals('{"testKey":"testPausedValue"}', formState);
    }

    @isTest
    static void testGetPausedFormState_NotFound() {
        Id nonExistentId = UserInfo.getUserId(); 
        Exception ex;
        Test.startTest();
        try {
            DisbursementFormController.getPausedFormState(nonExistentId);
            ex = null;
        } catch (AuraHandledException e) {
            ex = e;
        }
        Test.stopTest();

        System.assertNotEquals(null, ex, 'AuraHandledException expected for invalid Paused_Flow__c ID.');
    }

    @isTest
    static void testSavePausedFormState_New() {
        Project__c proj = [SELECT Id FROM Project__c WHERE Name = 'Test Project Alpha' LIMIT 1];
        String jsonState = '{"newStateData":"newPausedData"}';

        Test.startTest();
        Id pausedFlowId = DisbursementFormController.savePausedFormState(
            jsonState, null, proj.Id, 'Initial Pause'
        );
        Test.stopTest();

        System.assertNotEquals(null, pausedFlowId);
        Paused_Flow__c pf = [SELECT Form_State_JSON__c, Status__c, Project__c, Pause_Reason__c
                             FROM Paused_Flow__c WHERE Id = :pausedFlowId];
        System.assertEquals(jsonState, pf.Form_State_JSON__c);
        System.assertEquals('Paused', pf.Status__c);
        System.assertEquals(proj.Id, pf.Project__c);
        System.assertEquals('Initial Pause', pf.Pause_Reason__c);
    }

    @isTest
    static void testSavePausedFormState_Update() {
        Project__c proj = [SELECT Id FROM Project__c WHERE Name = 'Test Project Alpha' LIMIT 1];
        Paused_Flow__c existingPf = new Paused_Flow__c(
            Project__c = proj.Id,
            Form_State_JSON__c = '{"oldStateData":"oldPausedData"}',
            Status__c = 'Paused',
            User__c = UserInfo.getUserId(),
            Pause_Reason__c = 'Old Reason'
        );
        insert existingPf;

        String newJsonState = '{"updatedStateData":"updatedPausedData"}';
        Test.startTest();
        Id pausedFlowId = DisbursementFormController.savePausedFormState(
            newJsonState, existingPf.Id, proj.Id, 'Updated Reason'
        );
        Test.stopTest();

        System.assertEquals(existingPf.Id, pausedFlowId);
        Paused_Flow__c pf = [SELECT Form_State_JSON__c, Pause_Reason__c 
                             FROM Paused_Flow__c WHERE Id = :pausedFlowId];
        System.assertEquals(newJsonState, pf.Form_State_JSON__c);
        System.assertEquals('Updated Reason', pf.Pause_Reason__c);
    }

    @isTest
    static void testSavePausedFormState_Exception_NullProjectIdForNew() {
        String jsonState = '{"failStateData":"fail"}';
        Exception ex;
        Test.startTest();
        try {
            DisbursementFormController.savePausedFormState(
                jsonState, null, null, 'No Project'
            );
            ex = null;
        } catch (AuraHandledException e) {
            ex = e;
        }
        Test.stopTest();
        //System.assertNotEquals(null, ex, 'AuraHandledException expected when projectId is null.');
    }

    @isTest
    static void testCompletePausedFormState_Success() {
        Project__c proj = [SELECT Id FROM Project__c WHERE Name = 'Test Project Alpha' LIMIT 1];
        Paused_Flow__c pf = new Paused_Flow__c(
            Project__c = proj.Id,
            Form_State_JSON__c = '{"someStateData":"somePausedValue"}',
            Status__c = 'Paused',
            User__c = UserInfo.getUserId()
        );
        insert pf;

        Test.startTest();
        DisbursementFormController.completePausedFormState(pf.Id);
        Test.stopTest();

        Paused_Flow__c updatedPf = [SELECT Status__c FROM Paused_Flow__c WHERE Id = :pf.Id];
        System.assertEquals('Submitted', updatedPf.Status__c);
    }

    @isTest
    static void testCompletePausedFormState_NoRecord_HandlesException() {
        Id nonExistentId = UserInfo.getUserId();
        Exception ex;
        Test.startTest();
        try {
            DisbursementFormController.completePausedFormState(nonExistentId);
            ex = null;
        } catch (Exception e) {
            ex = e;
        }
        Test.stopTest();
        System.assertEquals(null, ex, 'completePausedFormState should not throw on missing record.');
    }
}