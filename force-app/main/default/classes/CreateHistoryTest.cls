@IsTest
private class CreateHistoryTest {
    
    @TestSetup
    static void setupData() {
        // 1) Create a Project
        Project__c proj = new Project__c(Name = 'Test Project');
        insert proj;
        
        // 2) Create one Cashflow__c parent
        Cashflow__c cf = new Cashflow__c(
            Name       = 'Test Cashflow',
            Project__c = proj.Id
        );
        insert cf;
        
        // 3) Create 5 Cashflow_Line_Item__c records
        List<Cashflow_Line_Item__c> lines = new List<Cashflow_Line_Item__c>();
        for (Integer i = 0; i < 5; i++) {
            lines.add(new Cashflow_Line_Item__c(
                Cashflow__c           = cf.Id,
                Line_Item_Category__c             = 'Material'
            ));
        }
        insert lines;
    }
    
    @IsTest
    static void testSingleUpdateCreatesHistory() {
        // fetch one line item
        Cashflow_Line_Item__c li = [
            SELECT Id, Line_Item_Category__c
            FROM Cashflow_Line_Item__c
            LIMIT 1
        ];
        String oldAmt = li.Line_Item_Category__c;
        
        // make a change
        li.Line_Item_Category__c = 'Direct Payroll';
        
        Test.startTest();
            update li;
        Test.stopTest();
        
        // assert exactly one history record with the old Amount
        List<Cashflow_Line_Item_History__c> hist = [
            SELECT Cashflow_Line_Item__c, Line_Item_Category__c
            FROM Cashflow_Line_Item_History__c
            WHERE Cashflow_Line_Item__c = :li.Id
        ];
        System.assertEquals(
            1,
            hist.size(),
            'One history record should be created for single update'
        );
        System.assertEquals(
            oldAmt,
            hist[0].Line_Item_Category__c,
            'History record must capture the old Amount'
        );
    }
    
    @IsTest
    static void testBulkUpdateCreatesMultipleHistory() {
        // grab all 5 from setup
        List<Cashflow_Line_Item__c> allLines = [
            SELECT Id, Line_Item_Category__c
            FROM Cashflow_Line_Item__c
        ];
        // bump each Amount
        for (Cashflow_Line_Item__c li : allLines) {
            li.Line_Item_Category__c = 'Direct Payroll';
        }
        
        Test.startTest();
            update allLines;
        Test.stopTest();
        
        // now we should have 5 history records
        Integer histCount = [
            SELECT COUNT()
            FROM Cashflow_Line_Item_History__c
        ];
        System.assertEquals(
            allLines.size(),
            histCount,
            'One history record should be created per updated line item'
        );
    }
    
    @IsTest
    static void testNoHistoryWhenNoTrackedFieldChange() {
        // fetch one line item
        Cashflow_Line_Item__c li = [
            SELECT Id, Line_Item_Category__c
            FROM Cashflow_Line_Item__c
            LIMIT 1
        ];
        
        // count existing history (should be zero in this fresh test context)
        Integer beforeCount = [
            SELECT COUNT()
            FROM Cashflow_Line_Item_History__c
        ];
        
        // call update without changing any of the tracked fields
        // (this still fires the trigger, but our handler should skip)
        Test.startTest();
            update li;
        Test.stopTest();
        
        Integer afterCount = [
            SELECT COUNT()
            FROM Cashflow_Line_Item_History__c
        ];
        System.assertEquals(
            beforeCount,
            afterCount,
            'No new history records should be created when tracked fields do not change'
        );
    }
}