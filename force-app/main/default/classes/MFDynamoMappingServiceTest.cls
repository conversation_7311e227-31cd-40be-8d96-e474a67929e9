/**
 * @description Test class for MFDynamoMappingService
 * <AUTHOR>
 * @date 2025
 */
@isTest
private class MFDynamoMappingServiceTest {
    
    /**
     * @description Tests the getMappingRulesAsJson method
     */
    @isTest
    static void testGetMappingRulesAsJson() {
        // Execute the method
        Test.startTest();
        String jsonResult = MFDynamoMappingService.getMappingRulesAsJson();
        Test.stopTest();
        
        // Verify the result is not null or empty
        System.assertNotEquals(null, jsonResult, 'JSON result should not be null');
        
        // Deserialize the JSON to verify structure
        List<cashFlowSessionController.MappingRule> mappingRules = 
            (List<cashFlowSessionController.MappingRule>) JSON.deserialize(
                jsonResult, 
                List<cashFlowSessionController.MappingRule>.class
            );
        
        // Verify that we have mapping rules (this depends on the metadata in the org)
        // This test will pass even if there are no rules, but it's good to check the structure
        System.assertNotEquals(null, mappingRules, 'Mapping rules list should not be null');
    }
    
    /**
     * @description Tests the getMappingRulesForObject method
     */
    @isTest
    static void testGetMappingRulesForObject() {
        // Execute the method for Account object
        Test.startTest();
        List<MF_Dynamo_SF_Mapping__mdt> accountRules = 
            MFDynamoMappingService.getMappingRulesForObject('Account');
        Test.stopTest();
        
        // Verify the result is not null
        System.assertNotEquals(null, accountRules, 'Account rules should not be null');
        
        // Check that all returned rules are for the Account object
        for (MF_Dynamo_SF_Mapping__mdt rule : accountRules) {
            System.assertEquals('Account', rule.Target_Salesforce_Object_API_Name__c, 
                               'Rule should be for Account object');
        }
    }
    
    /**
     * @description Tests the getMappingRulesForIntakeType method
     */
    @isTest
    static void testGetMappingRulesForIntakeType() {
        // Execute the method for Construction intake type
        Test.startTest();
        List<MF_Dynamo_SF_Mapping__mdt> constructionRules = 
            MFDynamoMappingService.getMappingRulesForIntakeType('Construction');
        Test.stopTest();
        
        // Verify the result is not null
        System.assertNotEquals(null, constructionRules, 'Construction rules should not be null');
        
        // Check that all returned rules are for the Construction intake type or null
        for (MF_Dynamo_SF_Mapping__mdt rule : constructionRules) {
            if (rule.Intake_Context_Type__c != null) {
                System.assert(rule.Intake_Context_Type__c.contains('Construction'), 
                             'Rule should be for Construction intake type');
            }
        }
    }
}