<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>SubContractors - ScopeOfWork to LineItem</label>
    <protected>false</protected>
    <values>
        <field>Dynamo_Path__c</field>
        <value xsi:type="xsd:string">subContractors - subContractors[].scopeOfWork</value>
    </values>
    <values>
        <field>Intake_Context_Type__c</field>
        <value xsi:type="xsd:string">Construction, Purchase Order</value>
    </values>
    <values>
        <field>Intake_Form_Section__c</field>
        <value xsi:type="xsd:string">Sub-Contractors</value>
    </values>
    <values>
        <field>Is_Active__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>Notes__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Processing_Order__c</field>
        <value xsi:type="xsd:double">380.0</value>
    </values>
    <values>
        <field>Target_Salesforce_Field_API_Name__c</field>
        <value xsi:type="xsd:string">Subcontractor_Scope__c</value>
    </values>
    <values>
        <field>Target_Salesforce_Object_API_Name__c</field>
        <value xsi:type="xsd:string">Cashflow_Line_Item_Child__c</value>
    </values>
    <values>
        <field>UI_Field_Label__c</field>
        <value xsi:type="xsd:string">Scope of Work</value>
    </values>
</CustomMetadata>
