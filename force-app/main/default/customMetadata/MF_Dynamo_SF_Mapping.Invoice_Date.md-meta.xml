<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>InvoiceSchedule - InvoicesDate</label>
    <protected>false</protected>
    <values>
        <field>Dynamo_Path__c</field>
        <value xsi:type="xsd:string">invoiceSchedule - invoiceSchedule[].date</value>
    </values>
    <values>
        <field>Intake_Context_Type__c</field>
        <value xsi:type="xsd:string">Purchase Order</value>
    </values>
    <values>
        <field>Intake_Form_Section__c</field>
        <value xsi:type="xsd:string">Invoice Schedule (PO)</value>
    </values>
    <values>
        <field>Is_Active__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>Notes__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Processing_Order__c</field>
        <value xsi:type="xsd:double">591.0</value>
    </values>
    <values>
        <field>Target_Salesforce_Field_API_Name__c</field>
        <value xsi:type="xsd:string">Week_Start_Date__c</value>
    </values>
    <values>
        <field>Target_Salesforce_Object_API_Name__c</field>
        <value xsi:type="xsd:string">Cashflow_Line_Item_Child__c</value>
    </values>
    <values>
        <field>UI_Field_Label__c</field>
        <value xsi:type="xsd:string">Date</value>
    </values>
</CustomMetadata>
