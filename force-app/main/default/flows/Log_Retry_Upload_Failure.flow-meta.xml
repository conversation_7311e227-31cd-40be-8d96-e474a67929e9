<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Dropbox_Retry_Upload</name>
        <label>Dropbox Retry Upload</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <actionName>DropboxController</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Update_Log_Is_Re_Synced</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Failure</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>contentVersionIds</name>
            <value>
                <elementReference>recordId.Error_RecordId__c</elementReference>
            </value>
        </inputParameters>
        <nameSegment>DropboxController</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <environments>Default</environments>
    <interviewLabel>Log - Retry Upload Failure {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Log - Retry Upload Failure</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordUpdates>
        <name>Update_Log_Is_Re_Synced</name>
        <label>Update Log -- Is Re-Synced</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <connector>
            <targetReference>Retry_Upload</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Is_ReSynced__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Custom_Exception__c</object>
    </recordUpdates>
    <screens>
        <name>Failure</name>
        <label>Failure</label>
        <locationX>440</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>failMessage</name>
            <fieldText>&lt;p&gt;Error - Something went wrong, please contact system administrator with following details&lt;/p&gt;&lt;p&gt;{!$Flow.FaultMessage}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Retry_Upload</name>
        <label>Retry Upload</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>message</name>
            <fieldText>&lt;p&gt;Your retry request has been successfully submitted.&lt;/p&gt;&lt;p&gt;It may take up to 15mins to reflect changes.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Dropbox_Retry_Upload</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Custom_Exception__c</objectType>
    </variables>
</Flow>
