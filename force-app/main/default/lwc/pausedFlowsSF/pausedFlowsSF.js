import { LightningElement, wire, api, track } from 'lwc';
import getPausedFlows from '@salesforce/apex/PausedFlowsSFController.getPausedFlows';
import discardFlowInterview from '@salesforce/apex/PausedFlowsSFController.discardFlowInterview';
import LightningConfirm from 'lightning/confirm';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { refreshApex } from '@salesforce/apex'; // Import refreshApex

const CARD_CLASSES_NO_RECORDS = 'slds-box';
const CARD_CLASSES_WITH_RECORDS = 'slds-box';

export default class PausedFlowsSF extends LightningElement {
    @api recordId;

    @track pausedFlows = [];
    @track limitedPausedFlows = [];

    @track error;
    @track showViewAllButton = false;
    @track viewAllData = false;

    limit = 6;
    @track loaded = false;
    @track isLoadingAction = false; // Used for discard action

    validLabels = ['Disbursement Request Form', 'Existing Client', 'Application'];

    _wiredFlowsResult; // To store the provisioned object from the wire for refreshApex

    @wire(getPausedFlows, { accountId: '$recordId' })
    wiredPausedFlows(result) {
        this._wiredFlowsResult = result; // Store the provisioned result
        const { data, error } = result;
        if (data) {
            const allDisplayableItems = data
                .map(item => ({
                    ...item,
                    // Example: Ensure ExtractedInterviewLabel exists, provide default if not
                    ExtractedInterviewLabel: item.ExtractedInterviewLabel || 'Unknown Flow',
                    isFlowInterview: item.ItemType === 'FlowInterview'
                }))
                .filter(item =>
                    this.validLabels.includes(item.ExtractedInterviewLabel)
                );

            this.pausedFlows = [...allDisplayableItems];
            this.limitedPausedFlows = allDisplayableItems.slice(0, this.limit);
            this.showViewAllButton = allDisplayableItems.length > this.limit && !this.viewAllData;
            // Reset viewAllData state only if data is refreshed and user wasn't already viewing all
            // If viewAllData is true, we want to keep it true unless explicitly changed.
            // The wire service re-runs on recordId change, so resetting viewAllData might be needed
            // if the context changes entirely. For now, let's manage it carefully.
            // If !this.viewAllData, it's safe to ensure button visibility is based on current limit.
            if (!this.viewAllData) {
                 this.showViewAllButton = allDisplayableItems.length > this.limit;
            }

            this.error = undefined;
            this.loaded = true;
        } else if (error) {
            console.error('Error fetching paused flows via wire:', JSON.stringify(error));
            this.error = error;
            this.pausedFlows = [];
            this.limitedPausedFlows = [];
            this.showViewAllButton = false;
            this.loaded = true; // Still loaded, but with an error state
        }
    }

    async handleDiscardClick(event) {
        const flowInterviewId = event.target.dataset.id;
        if (!flowInterviewId) {
            this.showToast('Error', 'Could not identify the flow to discard.', 'error');
            return;
        }

        const confirmResult = await LightningConfirm.open({
            message: 'Are you sure you want to discard this incomplete submission? This action cannot be undone.',
            variant: 'headerless',
            label: 'Confirm Discard',
        });

        if (!confirmResult) {
            return;
        }

        this.isLoadingAction = true;
        // this.loaded = false; // fetchPausedFlowsAndUpdate or refreshApex will handle loaded state changes

        try {
            const resultMessage = await discardFlowInterview({ flowInterviewId: flowInterviewId });
            this.showToast('Success', resultMessage, 'success');

            // --- OPTION 1: Optimistic Update + Imperative Refresh (Recommended for UX) ---
            // Optimistically remove the item from local arrays for immediate UI update
            this.pausedFlows = this.pausedFlows.filter(flow => flow.Id !== flowInterviewId);
            // Re-evaluate limitedPausedFlows and showViewAllButton based on the new state of pausedFlows
            if (this.viewAllData) {
                // If viewing all, limitedPausedFlows is not the direct source for display,
                // but it's good to keep it consistent or simply rely on pausedFlows for display.
                // The template should iterate over `pausedFlows` when `viewAllData` is true.
            } else {
                // If not viewing all, update limitedPausedFlows
                this.limitedPausedFlows = this.pausedFlows.slice(0, this.limit);
            }
            // Update button visibility based on the new state of pausedFlows
            this.showViewAllButton = this.pausedFlows.length > this.limit && !this.viewAllData;


            // Then, refresh from server to ensure consistency.
            // This will call the @wire method again.
            if (this._wiredFlowsResult) {
                await refreshApex(this._wiredFlowsResult);
            } else {
                // Fallback if wire result isn't available for some reason, though it should be.
                await this.fetchPausedFlowsAndUpdate();
            }
            // Note: refreshApex will cause `wiredPausedFlows` to re-execute,
            // which will update `loaded`, `pausedFlows`, `limitedPausedFlows`, etc.

            // --- OPTION 2: Just call fetchPausedFlowsAndUpdate (Simpler, but UI waits for server) ---
            // await this.fetchPausedFlowsAndUpdate();

        } catch (error) {
            console.error('Error discarding flow interview:', JSON.stringify(error));
            this.showToast('Error', this.getErrorMessage(error), 'error');
            // If discard failed, we should definitely refresh from server to get the true state.
            if (this._wiredFlowsResult) {
                await refreshApex(this._wiredFlowsResult);
            } else {
                await this.fetchPausedFlowsAndUpdate();
            }
        } finally {
            this.isLoadingAction = false;
            // `loaded` state is managed by the wire service or fetchPausedFlowsAndUpdate
        }
    }

    // This method can be used if you need an imperative way to refresh data,
    // for example, from a manual refresh button.
    // Using refreshApex is generally preferred if the data comes from a wire.
    async fetchPausedFlowsAndUpdate() {
        this.loaded = false;
        try {
            const data = await getPausedFlows({ accountId: this.recordId });
            if (data) {
                const allDisplayableItems = data
                    .map(item => ({
                        ...item,
                        ExtractedInterviewLabel: item.ExtractedInterviewLabel || 'Unknown Flow',
                        isFlowInterview: item.ItemType === 'FlowInterview'
                    }))
                    .filter(item =>
                        this.validLabels.includes(item.ExtractedInterviewLabel)
                    );

                this.pausedFlows = [...allDisplayableItems];
                if (this.viewAllData) {
                    // If viewing all, limited list isn't primary, but can be kept in sync or ignored for display
                    this.limitedPausedFlows = allDisplayableItems.slice(0, this.limit); // Keep it for consistency
                } else {
                    this.limitedPausedFlows = allDisplayableItems.slice(0, this.limit);
                }
                this.showViewAllButton = allDisplayableItems.length > this.limit && !this.viewAllData;
                this.error = undefined;
            } else {
                // Handle case where data is null (e.g., Apex returns null for no records)
                this.pausedFlows = [];
                this.limitedPausedFlows = [];
                this.showViewAllButton = false;
                this.error = undefined; // No data isn't necessarily an error
            }
        } catch (error) {
            console.error('Error re-fetching paused flows imperatively:', JSON.stringify(error));
            this.error = error;
            this.pausedFlows = [];
            this.limitedPausedFlows = [];
            this.showViewAllButton = false;
        } finally {
            this.loaded = true;
        }
    }

    // Handles the "View All" button click
    handleViewAllClick() {
        this.viewAllData = true;
        this.showViewAllButton = false; // Hide "View All" button when all are shown
        // No need to change limitedPausedFlows here, template will use pausedFlows
    }

    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({ title, message, variant }));
    }

    getErrorMessage(error) {
        if (!error) {
            return 'An unknown error occurred.';
        }
        if (error.body) {
            if (Array.isArray(error.body)) {
                // Handle array of errors (common from Apex)
                const messages = error.body.map(e => e.message).filter(Boolean);
                if (messages.length > 0) return messages.join(', ');
            } else if (typeof error.body.message === 'string') {
                // Handle single error message
                return error.body.message;
            }
        }
        if (typeof error.message === 'string') {
            return error.message;
        }
        // Fallback for other error structures
        try {
            return JSON.stringify(error);
        } catch (e) {
            // If stringify fails
            return 'Could not parse error details.';
        }
    }

    get hasPausedFlows() {
        return this.pausedFlows && this.pausedFlows.length > 0;
    }

    get itemsToDisplay() {
        return this.viewAllData ? this.pausedFlows : this.limitedPausedFlows;
    }

    get cardClasses() {
        return this.hasPausedFlows ? CARD_CLASSES_WITH_RECORDS : CARD_CLASSES_NO_RECORDS;
    }

    get formattedError() {
        return this.error ? this.getErrorMessage(this.error) : '';
    }

    /*
    // This method seems unused and relies on `lastDiscardedId` which is not set.
    // Consider removing it if it's not part of another functionality.
    // If it was intended for optimistic updates, the logic in handleDiscardClick is more direct.
    refreshData() {
        // this.pausedFlows = this.pausedFlows.filter(flow => flow.Id !== this.lastDiscardedId);
        // this.pausedFlows = JSON.parse(JSON.stringify(this.pausedFlows));
        // this.limitedPausedFlows = this.limitedPausedFlows.filter(flow => flow.Id !== this.lastDiscardedId);
        // this.limitedPausedFlows = JSON.parse(JSON.stringify(this.limitedPausedFlows));
        // this.fetchPausedFlowsAndUpdate(); // Or refreshApex(this._wiredFlowsResult);
        console.warn('refreshData() was called, but it might be deprecated or incomplete.');
        if (this._wiredFlowsResult) {
            refreshApex(this._wiredFlowsResult);
        } else {
            this.fetchPausedFlowsAndUpdate();
        }
    }
    */
}