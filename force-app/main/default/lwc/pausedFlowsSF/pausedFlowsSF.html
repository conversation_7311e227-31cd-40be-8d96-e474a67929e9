<template>
    <div class="exampleHolder">
        <template if:false={loaded}>
            <lightning-spinner alternative-text="Loading"></lightning-spinner>
        </template>
    </div>
    <div class={cardClasses} if:true={loaded}>
         <article class="slds-card">
            <div class="slds-card__header slds-grid">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__figure">
                        <span class="slds-icon_container slds-icon-standard-contact" title="contact">
                            <lightning-icon icon-name="standard:flow" alternative-text="Flow" title="Flow"></lightning-icon>
                        </span>
                    </div>
                    <div class="slds-media__body">
                        <h3 class="slds-card__header-title">
                            Incomplete Submissions ({pausedFlows.length})
                        </h3>
                    </div>
                </header>
            </div>

            <template if:true={hasPausedFlows}>
                <div class="slds-card__body">
                    <table class="slds-table slds-table_cell-buffer slds-no-row-hover slds-table_bordered slds-table_fixed-layout">
                        <thead>
                            <tr class="slds-line-height_reset">
                                <th scope="col">
                                    <div class="slds-truncate" title="Paused Reason">Paused Reason</div>
                                </th>
                                <th scope="col">
                                    <div class="slds-truncate" title="Flow Name">Form/Flow Name</div>
                                </th>
                                <th scope="col">
                                    <div class="slds-truncate" title="Owner">Owner</div>
                                </th>
                                <th scope="col">
                                    <div class="slds-truncate" title="Draft Date">Draft Date</div>
                                </th>
                                <th scope="col">
                                    <div class="slds-truncate" title="Actions">Actions</div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <template if:false={viewAllData}>
                                <template for:each={limitedPausedFlows} for:item="flow">
                                    <tr key={flow.Id}>
                                        <td>
                                            <div class="slds-truncate" title={flow.PauseLabel}>{flow.PauseLabel}</div>
                                        </td>
                                        <td>
                                            <div class="slds-truncate" title={flow.ExtractedInterviewLabel}>
                                                {flow.ExtractedInterviewLabel}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="slds-truncate" title={flow.OwnerName}>{flow.OwnerName}</div>
                                        </td>
                                        <td>
                                            <div class="slds-truncate" title={flow.CreateDD}>{flow.CreateDD}</div>
                                        </td>
                                        <td data-label="Actions">
                                            <lightning-button
                                                label="Discard"
                                                variant="destructive-text"
                                                icon-name="utility:delete"
                                                icon-position="left"
                                                data-id={flow.Id}
                                                onclick={handleDiscardClick}
                                                class="slds-m-left_xx-small">
                                            </lightning-button>
                                        </td>
                                    </tr>
                                </template>
                            </template>

                            <template if:true={viewAllData}>
                                <template for:each={pausedFlows} for:item="flow">
                                    <tr key={flow.Id}>
                                        <td>
                                            <div class="slds-truncate" title={flow.PauseLabel}>{flow.PauseLabel}</div>
                                        </td>
                                        <td>
                                            <div class="slds-truncate" title={flow.ExtractedInterviewLabel}>
                                                {flow.ExtractedInterviewLabel}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="slds-truncate" title={flow.OwnerName}>{flow.OwnerName}</div>
                                        </td>
                                        <td>
                                            <div class="slds-truncate" title={flow.CreateDD}>{flow.CreateDD}</div>
                                        </td>
                                        <td data-label="Actions">
                                            <lightning-button
                                                label="Discard"
                                                variant="destructive-text"
                                                icon-name="utility:delete"
                                                icon-position="left"
                                                data-id={flow.Id}
                                                onclick={handleDiscardClick}
                                                class="slds-m-left_xx-small">
                                            </lightning-button>
                                        </td>
                                    </tr>
                                </template>
                            </template>
                        </tbody>
                    </table>
                </div>

                <template if:true={showViewAllButton}>
                    <footer class="slds-card__footer">
                        <a class="slds-card__footer-action" href="javascript:void(0);" onclick={handleViewAllClick} role="button">
                            View All
                        </a>
                    </footer>
                </template>
            </template>
            <template if:false={hasPausedFlows}>
                 <div class="slds-card__body slds-card__body_inner">
                    No incomplete submissions found.
                </div>
            </template>
            <template if:true={error}>
                <div class="slds-card__body slds-card__body_inner">
                    <p class="slds-text-color_error">{formattedError}</p>
                </div>
            </template>
        </article>
    </div>
    
    
    
</template>