import { LightningElement, wire, track } from 'lwc';
import { CurrentPageReference, NavigationMixin } from 'lightning/navigation';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import communityId from "@salesforce/community/Id";
import { loadScript } from 'lightning/platformResourceLoader';
import iframeResizerLib from '@salesforce/resourceUrl/IframeResizer'; 

export default class CashFlowCmp extends NavigationMixin(LightningElement) {
    @track iframeUrl; 
    iFrameLibInitialized = false;

    @track acctId;
    @track opptyId;
    @track usrId;

    @wire(CurrentPageReference)
    getPageReference(currentPageReference) {
        if (currentPageReference) {
            console.log('currentPageReference --> ' + JSON.stringify(currentPageReference));
            const { state } = currentPageReference;
            this.acctId = state.acctId;
            console.log('accountId --> ' + this.acctId);
            this.usrId = state.usrId;
            console.log('userId --> ' + this.usrId);
        }
    }

    connectedCallback() {
        console.log('communityId --> ' + communityId);
        if ((this.acctId && this.usrId) || communityId !== undefined) {
            this.iframeUrl = `https://mobilizationfunding.com/portal/cash-flow-embed/?hide_layout=true&sf_user_id=${this.usrId}&sf_account_id=${this.acctId}`;
            console.log('iframeUrl --> ' + this.iframeUrl);
        } else {
            this.showToast('Error', 'Invalid Cash Flow iframe URL. Redirecting to Home.', 'error');
            console.error('Missing parameters for iframe URL');
            this.redirectToHome();
        }

        window.addEventListener('message', (event) => {
            console.log('event --> ' + JSON.stringify(event));
            if (event.origin !== "https://mobilizationfunding.com") {
                return;
            }
            if (event.data?.type === 'MfWeb-CashFlow-Complete') {
                console.log('Cash Flow process completed. Redirecting to home page...');
                this.redirectToHome();
            }
        }, false);
    }

    renderedCallback() {
        if (this.iFrameLibInitialized || !this.iframeUrl) {
            return;
        }
        this.iFrameLibInitialized = true;

        loadScript(this, iframeResizerLib)
            .then(() => {
                const iframeEl = this.template.querySelector('iframe');
                if (iframeEl && window.iFrameResize) {
                    window.iFrameResize({
                        log: false,
                        checkOrigin: ['https://mobilizationfunding.com'], 
                        heightCalculationMethod: 'max',
                        autoResize: true,
                        sizeWidth: true
                    }, iframeEl);
                    console.log('iframe-resizer initialized.');
                }
            })
            .catch(error => {
                console.error('Failed to load iframe resizer:', error);
            });
    }

    redirectToHome() {
        this[NavigationMixin.Navigate]({
            type: 'standard__namedPage',
            attributes: {
                pageName: 'home'
            }
        });
    }

    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({ title, message, variant }));
    }
}