<template>
    <div class="slds-is-relative">
        <template if:true={isLoading}>
            <div class="slds-is-absolute slds-align_absolute-center" style="z-index: 9999;">
                <lightning-spinner alternative-text="Loading..." size="medium"></lightning-spinner>
            </div>
        </template>
        <div class="table-wrapper slds-is-relative">
            <!-- ✦ the chevron toggle button -->
            <!-- <lightning-button-icon class="drawer-toggle-button" icon-name={drawerIconName}
                alternative-text="Toggle Details Drawer" variant="bare" onclick={toggleDrawer}></lightning-button-icon> -->
            <template if:false={isLoading}>
                <div class="slds-scrollable_x slds-is-relative" data-id="grid-container" style="overflow: visible;">
                    <table
                        class="slds-table slds-table_cell-buffer slds-table_bordered slds-table_fixed-layout cashflow-table">
                        <thead>
                            <tr class="header-row-1">
                                <th class="slds-col_pinned slds-cell-fixed header-fixed-col header-top-left" scope="col"
                                    style="width: 250px;">
                                    <div class="slds-truncate header-main-title">PROJECT WEEK</div>
                                </th>
                                <template for:each={weekColumns} for:item="weekCol" for:index="colIndex">
                                    <th key={weekCol.id1} scope="col" class="slds-text-align_center week-header-cell">
                                        <div class="slds-truncate">{weekCol.label}</div>
                                    </th>
                                </template>
                                <th if:true={showTotalColumnInHeader} class="total-header" scope="col">TOTALS</th>
                            </tr>
                            <tr class="header-row-2">
                                <th class="slds-col_pinned slds-cell-fixed header-fixed-col" scope="col"
                                    style="width: 250px;">
                                    <div class="slds-truncate header-sub-title">Week Ending (Friday)</div>
                                </th>
                                <template for:each={weekColumns} for:item="weekCol" for:index="colIndex">
                                    <th key={weekCol.id2} scope="col" class="slds-text-align_center date-header-cell">
                                        <div class="slds-truncate" title={weekCol.formattedDate}>{weekCol.formattedDate}
                                        </div>
                                    </th>
                                </template>
                                <th if:true={showTotalColumnInHeader} class="total-header"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <template for:each={cashFlowData} for:item="row" for:index="rowIndex">
                                <template if:true={row.isSectionHeader}>
                                    <tr key={row.id} class="section-header-row">
                                        <th colspan={totalColumnCountInternalPlusTotal} scope="colgroup"
                                            class="section-title-cell">
                                            <div
                                                class="slds-grid slds-grid_align-spread slds-p-vertical_x-small slds-p-horizontal_small section-title-wrapper">
                                                <span class="section-label">{row.label}</span>
                                                <template if:true={hasFilteredCategories}>
                                                <template if:true={row.showAddButton}>
                                                    <lightning-button-icon icon-name="utility:add"
                                                        alternative-text="Add line" variant="bare" size="small"
                                                        class="slds-m-right_x-small" data-section-id={row.sectionId}
                                                        onclick={handleAddItem}>
                                                    </lightning-button-icon>
                                                </template>
                                                </template>
                                            </div>
                                        </th>
                                    </tr>
                                </template>
                                <template if:false={row.isSectionHeader}>
                                    <tr key={row.id} class={row.rowClass}>
                                        <th class="slds-col_pinned slds-cell-fixed" scope="row" style={row.rowStyle}>
                                            <div class="slds-grid slds-grid_vertical-align-center slds-grid_align-spread"
                                                style={row.indentStyle}>
                                                <div class="slds-truncate slds-grid slds-grid_vertical-align-center">
                                                    <template if:true={row.isExpenseCategoryChild}>
                                                        <lightning-icon icon-name="utility:subscription" size="x-small"
                                                            class="slds-m-right_x-small category-icon"></lightning-icon>
                                                    </template>
                                                    <template if:true={row.isExpenseCategoryParent}>
                                                        <lightning-icon icon-name="utility:check" size="x-small"
                                                            class="slds-m-right_x-small category-icon"></lightning-icon>
                                                    </template>

                                                    <template if:false={row.isNew}>
                                                        <span class={row.labelClass} title={row.label}>{row.label}</span>
                                                    </template>

                                                </div>

                                                <template if:true={row.showDeleteButton}>
                                                    <lightning-button-icon icon-name="utility:recycle_bin_empty"
                                                        alternative-text="Delete row" title="Delete row" variant="bare"
                                                        size="small" class="slds-m-left_x-small" data-row-id={row.id}
                                                        onclick={handleDeleteRowClick} style="margin-right:17px;">
                                                    </lightning-button-icon>
                                                </template>
                                            </div>
                                        </th>
                                        <template for:each={row.weeks} for:item="cell" for:index="colIndex">
                                            <td key={cell.id} data-label={cell.weekLabel} class={cell.computedClass}
                                                style={row.cellStyle}>
                                                <template if:true={cell.isDifference}>
                                                    <button class="slds-button slds-button_reset cell-button slds-truncate"
                                                        title="Edit Details"
                                                        onclick={handleCellClick}
                                                        data-row-index={rowIndex}
                                                        data-col-index={colIndex}>
                                                    <lightning-formatted-number
                                                        value={cell.value}
                                                        format-style="currency"
                                                        currency-code="USD"
                                                        class="cell-value-display">
                                                    </lightning-formatted-number>
                                                </button>
                                                </template>
                                                <template if:false={cell.isDifference}>
                                                    <lightning-formatted-number value={cell.value}
                                                        format-style="currency" currency-code="USD"
                                                        class="cell-value-display">
                                                    </lightning-formatted-number>
                                                </template>
                                            </td>
                                        </template>
                                        <td if:true={row.showTotalCell} class="calculated-total slds-text-align_center">
                                            <lightning-formatted-number value={row.rowTotalValue}
                                                format-style="currency" currency-code="USD">
                                            </lightning-formatted-number>
                                        </td>
                                    </tr>
                                </template>
                            </template>
                        </tbody>
                    </table>

                    <c-cashflow-edit-modal is-visible={isModalVisible} modal-title="Edit Linked (Fixed) Line Item"
                        expense-category-options={expenseCategoryOptions}
                        payment-frequency-options={paymentFrequencyOptionsInternal}
                        payment-term-options={paymentTermOptions} onsave={handleModalSave} onunlink={handleModalUnlink}
                        ondeleteitem={handleModalDelete} onclosemodal={handleCloseModal}
                        ontoasterror={handleModalToastError} object-api-name-for-form="Cashflow_Line_Item__c">
                    </c-cashflow-edit-modal>
                </div>
            </template>
            <!-- <aside if:true={isDrawerOpen} class="slds-drawer slds-drawer_right slds-is-open" role="dialog"
                aria-labelledby="drawer-heading">
                <div class="slds-drawer__header">
                    <button class="slds-button slds-button_icon slds-button_icon-inverse"
                title="Close"
                onclick={toggleDrawer}>
          <lightning-icon icon-name="utility:close" alternative-text="Close"></lightning-icon>
          <span class="slds-assistive-text">Close</span>
        </button>
                    <h2 id="drawer-heading" class="slds-text-heading_medium">Dummy Details</h2>
                </div>
                <div class="slds-drawer__content">
                   
                    <p><strong>Week 18:</strong> $1,000,000.00</p>
                    <p><strong>Week 19:</strong> $1,000,000.00</p>
                    <p><strong>Week 20:</strong> $1,000,000.00</p>
                    <p><strong>Week 21:</strong> $1,000,000.00</p>
                </div>
            </aside> -->
            <!-- <div if:true={isDrawerOpen} class="slds-backdrop slds-backdrop_open"></div> -->
        </div>
    </div>
    <template if:true={isCategoryModalOpen}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open" aria-modal="true">
            <div class="slds-modal__container slds-modal__container_small">

                <!-- Header with close icon -->
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close"
                  title="Close"
                  onclick={closeCategoryModal}>
            <lightning-icon icon-name="utility:close"
                            alternative-text="Close"></lightning-icon>
            <span class="slds-assistive-text">Close</span>
          </button>
                    <h2 class="slds-text-heading_medium">
                        Select Expense Category
                    </h2>
                </header>

                <!-- Radio-Group for single choice -->
                <div class="slds-modal__content slds-p-around_medium">
                    <lightning-radio-group name="categoryPicker" options={filteredExpenseCategoryOptions}
                        value={selectedCategory} type="radio" onchange={handleRadioChange}>
                    </lightning-radio-group>
                </div>

                <!-- Footer -->
                <footer class="slds-modal__footer slds-grid slds-grid_align-end">
                    <lightning-button label="Cancel" onclick={closeCategoryModal}>
                    </lightning-button>
                    <!-- <lightning-button label="Add" variant="brand" onclick={confirmCategorySelection}
                        disabled={isAddDisabled}>
                    </lightning-button> -->
                    <button
                            class="custom-green-button slds-button slds-m-left_xx-small  "
                            onclick={confirmCategorySelection}
                            disabled={isAddDisabled}>
                            Add
                        </button>
                </footer>

            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>