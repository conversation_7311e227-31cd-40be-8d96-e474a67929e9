/* requestItemRepeater.css */
:host {
    /* Variables for theming if needed, though direct styling is used below */
}

.repeater-container {
    /* background-color: #f9f9f9; 
    padding: 1rem; */
    border-radius: 0.25rem; /* Standard SLDS card radius */
}

.item-card {
    background-color: #ffffff; /* White background for each item card */
    border: 1px solid #e0e0e0; /* Softer border for cards */
}

.item-section-title {
    color: rgb(26, 47, 101); /* Darker blue for item title, adjust as per screenshot */
    font-family: 'Utopia Std', serif; /* As per global CSS, if it applies */
    font-weight: 600; /* Semibold */
}

.file-upload-title {
    color: #333333; /* Standard dark grey for section titles */
    font-family: 'Proxima Nova', sans-serif; /* As per global CSS */
    font-weight: 600; /* Semibold */
}

/* Styling for lightning-input to match the provided CSS */
.custom-input lightning-primitive-input-simple input, /* For text, date, number etc. */
.custom-input .slds-input { /* Fallback or direct styling if primitive isn't specific enough */
    border-radius: 0px !important;
    background: #ffffff !important;
    box-shadow: 4px 4px 8px #a3a3a3 !important;
    border: 1px solid #dddbda !important; /* SLDS default border color, ensure it's visible with shadow */
}

/* Ensure labels inherit community styles or define explicitly if needed */
/* The global CSS for .slds-form-element__label should apply.
   If not, uncomment and adjust: */
/*
.custom-input .slds-form-element__label {
    color: #2cac68;
    font-family: 'Proxima Nova', sans-serif;
    font-size: 12px;
    font-weight: bold;
}
*/

.add-item-button {
    /* SLDS variant="neutral" is usually good. Add custom styles if needed to match screenshot. */
    /* e.g., background-color: #4CAF50; color: white; */
}

.remove-item-button lightning-primitive-icon,
.remove-file-button lightning-primitive-icon {
     /* Ensure icon color is visible, SLDS default is usually fine */
    --sds-c-icon-color-foreground-default: #ea001e; /* Red for delete actions */
}
.remove-item-button:disabled lightning-primitive-icon {
    --sds-c-icon-color-foreground-default: #c9c9c9; /* Grey out when disabled */
}


.file-upload-section {
    border-top: 1px dashed #dddbda;
    padding-top: 1rem;
    margin-top: 1rem;
}

.uploaded-files-container ul {
    list-style-type: none; /* Remove default bullets if any */
    padding-left: 0;
}

.file-name-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid #f3f3f3; /* Light separator for file names */
}
.file-name-item:last-child {
    border-bottom: none;
}

.file-name-item span {
    font-family: 'Proxima Nova', sans-serif;
    font-size: 0.875rem;
}

/* Adjustments for lightning-file-upload styling if needed */
/* For example, to make the drop zone more prominent or match a specific theme */
/*
lightning-file-upload .slds-file-selector__dropzone {
    border: 2px dashed #0070d2;
}
*/