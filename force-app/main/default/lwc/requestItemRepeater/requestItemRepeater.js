/* eslint-disable */
import { publish, MessageContext } from 'lightning/messageService';
import ADVANCE_AMOUNT_UPDATE from '@salesforce/messageChannel/advanceAmountUpdate__c';
import { LightningElement, api, track, wire } from 'lwc';
import { FlowAttributeChangeEvent } from 'lightning/flowSupport';
import { ShowToastEvent }    from 'lightning/platformShowToastEvent';
import deleteUploadedFileServer from '@salesforce/apex/RequestItemController.deleteUploadedFile';

const DEFAULT_ITEM_SECTION_TITLE         = "Item ";
const DEFAULT_FILE_UPLOAD_SECTION_TITLE = "Upload Supporting Documents";
const DEFAULT_FILE_UPLOAD_LABEL         = "Upload Invoice, Receipts, etc.";
const DEFAULT_ACCEPTED_FORMATS          = '.pdf,.png,.jpg,.jpeg,.doc,.docx,.xls,.xlsx,.txt';

export default class RequestItemRepeater extends LightningElement {
  // preserved IN props
  @api initialRequestItemsJSON;
  @api initialTotalInvoiceAmount;

  // new unified IN/OUT props
  @api requestItemsJSON;
  @api totalInvoiceAmount;
  @api requestItemsNew;

  // preserved OUT props (we’ll still emit to them)
  @api get requestItems() {
    return this._reactiveRequestItems.map(item => ({
      sobjectType: 'Requested_Item__c',
      Name:             item.Name,
      Description_Work__c: item.descriptionWork,
      Invoice_Date__c:  item.invoiceDate,
      Invoice_Amount__c: item.invoiceAmount,
      Invoice__c:       item.invoice,
      Invoice_Due_Date__c: item.invoiceDueDate
    }));
  }
  @api get requestItems2() {
    // match your FlowRequestItemInput structure
    return this._reactiveRequestItems.map(item => ({
      Name:             item.Name,
      descriptionWork:  item.descriptionWork,
      invoiceDate:      item.invoiceDate,
      invoiceAmount:    item.invoiceAmount,
      invoice:          item.invoice,
      invoiceDueDate:   item.invoiceDueDate,
      contentDocumentIds: item.contentDocumentIds
    }));
  }

  // config props
  @api itemSectionTitle         = DEFAULT_ITEM_SECTION_TITLE;
  @api fileUploadSectionTitle   = DEFAULT_FILE_UPLOAD_SECTION_TITLE;
  @api fileUploadLabel          = DEFAULT_FILE_UPLOAD_LABEL;
  @api acceptedFileFormats      = DEFAULT_ACCEPTED_FORMATS;
  @api removeFileUploadTitle    = false;

  @track _reactiveRequestItems   = [];
  @track _totalAmount            = 0;
  @track errorMessages           = [];

  _initialized = false;

 renderedCallback() {
  if (!this._initialized) {
    console.log('[Repeater] renderedCallback initializing... heinnnnnnnnnnn');
    this._loadFromFlowInputs();
    this._initialized = true;
  } else {
    console.log('[Repeater] renderedCallback re-render haaaaaaa');
    this._recalcTotal();
  }
}


  @wire(MessageContext)
  messageContext;

  _loadFromFlowInputs() {
    let arr = [];
    // prefer the unified JSON if present, else fall back to initialRequestItemsJSON
    const srcJSON = this.requestItemsJSON || this.initialRequestItemsJSON;
    if (srcJSON) {
      try {
        const parsed = JSON.parse(srcJSON);
        if (Array.isArray(parsed)) arr = parsed;
      } catch {
        this.errorMessages = ['Could not parse prior items'];
      }
    }
    if (!arr.length) {
      // start with one blank item
      arr = [{}];
    }
    // map into our internal model
    this._reactiveRequestItems = arr.map((itm, i) => ({
      id:                `item-${i+1}-${Date.now()}`,
      itemNumber:        i+1,
      Name:               itm.Name || '',
      descriptionWork:    itm.descriptionWork || '',
      invoiceDate:        itm.invoiceDate || null,
      invoiceAmount:      itm.invoiceAmount != null ? parseFloat(itm.invoiceAmount) : null,
      invoice:            itm.invoice || '',
      invoiceDueDate:     itm.invoiceDueDate || null,
      contentDocumentIds: itm.contentDocumentIds || [],
      uploadedFileNames:  itm.uploadedFileNames  || []
    }));
    this._recalcTotal();
    this._emitAllOutputs();
  }

  // add/remove
  handleAddItem() {
    const nextNum = this._reactiveRequestItems.length + 1;
    this._reactiveRequestItems = [
      ...this._reactiveRequestItems,
      {
        id:                `item-${nextNum}-${Date.now()}`,
        itemNumber:        nextNum,
        Name:               '',
        descriptionWork:    '',
        invoiceDate:        null,
        invoiceAmount:      null,
        invoice:            '',
        invoiceDueDate:     null,
        contentDocumentIds: [],
        uploadedFileNames:  []
      }
    ];
    this._recalcTotal();
    this._emitAllOutputs();
  }

  handleRemoveItem(e) {
    const rm = e.currentTarget.dataset.id;
    this._reactiveRequestItems = this._reactiveRequestItems
      .filter(i=>i.id!==rm)
      .map((i, idx)=>({ ...i, itemNumber: idx+1 }));
    if (!this._reactiveRequestItems.length) {
      this.handleAddItem();
    } else {
      this._recalcTotal();
      this._emitAllOutputs();
    }
  }

  // field change
  handleInputChange(e) {
    const id    = e.currentTarget.dataset.id;
    const fld   = e.currentTarget.name;
    let   val   = e.target.value;
    if (fld==='invoiceAmount') val = val ? parseFloat(val) : null;

    this._reactiveRequestItems = this._reactiveRequestItems.map(item =>
      item.id===id ? { ...item, [fld]: val } : item
    );
    this._recalcTotal();
    this._emitAllOutputs();
  }

  // _recalcTotal() {
  //   this._totalAmount = this._reactiveRequestItems
  //     .reduce((sum,i)=> sum + (i.invoiceAmount||0), 0);
  //     console.log('total amount '+this._totalAmount);
  // }

  _recalcTotal() {
    this._totalAmount = this._reactiveRequestItems
        .reduce((sum, i) => sum + (i.invoiceAmount || 0), 0);
    
    console.log('total amount: ' + this._totalAmount);

    // Publish updated amount via LMS
    publish(this.messageContext, ADVANCE_AMOUNT_UPDATE, {
        amount: this._totalAmount
    });
}
  // file upload/delete
  handleUploadFinished(e) {
    const itemId = e.currentTarget.dataset.id;
    const files  = e.detail.files;
    this._reactiveRequestItems = this._reactiveRequestItems.map(item => {
      if (item.id!==itemId) return item;
      const newIds    = files.map(f=>f.documentId);
      const newFiles  = files.map(f=>({ name:f.name, documentId:f.documentId }));
      const mergedIds = Array.from(new Set([...(item.contentDocumentIds||[]), ...newIds]));
      const nameMap   = new Map((item.uploadedFileNames||[]).map(f=>[f.documentId,f]));
      newFiles.forEach(nf=> nameMap.set(nf.documentId,nf));
      return {
        ...item,
        contentDocumentIds: mergedIds,
        uploadedFileNames:  Array.from(nameMap.values())
      };
    });
    this.showToast('Success', `${files.length} file(s) uploaded`, 'success');
    this._emitAllOutputs();
  }

  handleRemoveUploadedFile(e) {
    const itemId = e.currentTarget.dataset.itemid;
    const docId  = e.currentTarget.dataset.documentid;
    deleteUploadedFileServer({ contentDocumentId: docId })
      .then(()=> {
        this._reactiveRequestItems = this._reactiveRequestItems.map(item => {
          if (item.id!==itemId) return item;
          return {
            ...item,
            contentDocumentIds:   item.contentDocumentIds.filter(d=>d!==docId),
            uploadedFileNames:    item.uploadedFileNames.filter(f=>f.documentId!==docId)
          };
        });
        this.showToast('Deleted', 'File removed', 'success');
        this._emitAllOutputs();
      })
      .catch(err=> {
        this.showToast('Error', err.body?.message||err.message, 'error');
      });
  }

  // central emit
  _emitAllOutputs() {
    // debugger;
    // 1) unified IN/OUT
    const simpleArr = this._reactiveRequestItems.map(({ id,itemNumber, ...rest })=> rest);
    const json      = JSON.stringify(simpleArr);
    const tot       = this._totalAmount.toFixed(2);

    this.dispatchEvent(
      new FlowAttributeChangeEvent('requestItemsJSON', json)
    );
    this.dispatchEvent(
      new FlowAttributeChangeEvent('totalInvoiceAmount', tot)
    );
    this.dispatchEvent(
      new FlowAttributeChangeEvent('requestItemsNew', json)
    );
    this.dispatchEvent(
      new FlowAttributeChangeEvent('requestItems', this.requestItems)
    );
    this.dispatchEvent(
      new FlowAttributeChangeEvent('requestItems2', this.requestItems2)
    );
    const eventData = {
      requestItemsJSON: JSON.parse(json),
      totalInvoiceAmount: tot,
      requestItemsNew: JSON.parse(json),
      requestItems: this.requestItems, 
      requestItems2: this.requestItems2 
    };

    // 2. Dispatch the single 'change' event with the consolidated data
    this.dispatchEvent(new CustomEvent('change', {
      detail: eventData,
      bubbles: true, // Optional: Set to true if you want the event to bubble up the DOM
      composed: true // Optional: Set to true if you want the event to cross shadow boundaries
    }));
  }

  @api validate() {
    this._emitAllOutputs();
    this.errorMessages = [];
    let ok = true;

    this.template.querySelectorAll('lightning-input').forEach(inp => {
      inp.reportValidity();
      if (!inp.checkValidity()) ok = false;
    });

    if (!this._reactiveRequestItems.length) {
      this.errorMessages.push('At least one item is required.');
      ok = false;
    }

    if (!ok && !this.errorMessages.length) {
      this.errorMessages.push('Please review errors and complete all required fields.');
    }

    return ok
      ? { isValid: true }
      : { isValid: false, errorMessage: this.errorMessages.join(' ') };
  }

  showToast(title, msg, variant) {
    this.dispatchEvent(new ShowToastEvent({ title, message: msg, variant }));
  }

  /**
   * Dispatches a custom event with a specified name and value.
   * @param {string} eventName - The name of the event to dispatch (e.g., 'contactIdChange').
   * @param {*} detailValue - The value to be passed in the event's detail.
   * @param {boolean} bubbles - Whether the event should bubble up through the DOM. Defaults to true.
   * @param {boolean} composed - Whether the event can pass through shadow DOM boundaries. Defaults to true.
   */
  dispatchCustomEvent(eventName, detailValue, bubbles = true, composed = true) {
      if (typeof eventName !== 'string' || eventName.trim() === '') {
          console.error('Event name must be a non-empty string.');
          return;
      }

      const event = new CustomEvent(eventName, {
          detail: { value: detailValue }, // The payload of the event
          bubbles: bubbles,    // Whether the event bubbles up through the DOM
          composed: composed   // Whether the event can pass through shadow DOM boundaries
      });
      this.dispatchEvent(event);
      console.log(`Dispatched custom event: '${eventName}' with value:`, detailValue);
  }


  // template getters
  get reactiveRequestItems() {
    return this._reactiveRequestItems;
  }
  get isRemoveDisabled() {
    return this._reactiveRequestItems.length <= 1;
  }
  get showRemoveButton() {
    return true;
  }
  get acceptedFileFormatsArray() {
    return this.acceptedFileFormats.split(',').map(s=>s.trim());
  }
}