import { LightningElement, api, wire, track } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import getCashflowsForProject from '@salesforce/apex/ProjectCashflowController.getCashflowsForProject';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

const COLUMNS = [
    { label: 'Cashflow Name', fieldName: 'Name', type: 'text', sortable: true, initialWidth: 250 },
    { label: 'Status', fieldName: 'Status__c', type: 'text', sortable: true },
    { label: 'Version', fieldName: 'Version_Number__c', type: 'number', sortable: true, cellAttributes: { alignment: 'left' } },
    { label: 'Total Value', fieldName: 'Total_Project_Value__c', type: 'currency', sortable: true },
    { label: 'Forecast Start', fieldName: 'Forecast_Start_Date__c', type: 'date-local', sortable: true, typeAttributes:{ month:"2-digit", day:"2-digit", year:"numeric"} },
    { label: 'Project Start', fieldName: 'Project_Start_Date__c', type: 'date-local', sortable: true, typeAttributes:{ month:"2-digit", day:"2-digit", year:"numeric"} },
    { label: 'Created Date', fieldName: 'CreatedDate', type: 'date', sortable: true, typeAttributes:{ year:"numeric", month:"short", day:"2-digit", hour:"2-digit", minute:"2-digit"} },
    {
        type: 'button',
        initialWidth: 150,
        typeAttributes: {
            label: 'Open Cashflow',
            name: 'open_cashflow',
            title: 'Open this cashflow record',
            variant: 'brand-outline',
            iconName: 'utility:open'
        }
    }
];

export default class CashflowList extends NavigationMixin(LightningElement) {
    @api recordId; // Current Project__c record Id
    @track cashflows = [];
    @track error;
    @track isLoading = true;
    columns = COLUMNS;
    @track sortBy = 'CreatedDate';
    @track sortDirection = 'desc';

    wiredCashflowsResult; // To store the provisioned value from the wire service

    @wire(getCashflowsForProject, { projectId: '$recordId' })
    wiredCashflows(result) {
        this.isLoading = true;
        this.wiredCashflowsResult = result; // Store the result
        if (result.data) {
            this.cashflows = result.data.map(cf => ({...cf})); // Create a mutable copy
            this.error = undefined;
            this.sortData(this.sortBy, this.sortDirection); // Apply initial sort
            this.isLoading = false;
        } else if (result.error) {
            this.error = result.error;
            this.cashflows = [];
            this.errorMessage = this.reduceErrors(result.error).join(', ');
            this.showToast('Error Loading Cashflows', this.errorMessage, 'error');
            this.isLoading = false;
        }
    }

    handleRowAction(event) {
        const actionName = event.detail.action.name;
        const row = event.detail.row;

        if (actionName === 'open_cashflow') {
            if (row.Id) {
                this[NavigationMixin.Navigate]({
                    type: 'standard__navItemPage',
                    attributes: {
                        apiName: 'CashFlow' // API Name of your "CashFlow" Tab
                    },
                    state: {
                        c__recordId: row.Id, // Pass cashflowId in state
                        c__projectId: this.recordId
                    }
                });
            } else {
                this.showToast('Error', 'Cashflow ID is missing.', 'error');
            }
        }
    }

    handleSort(event) {
        this.sortBy = event.detail.fieldName;
        this.sortDirection = event.detail.sortDirection;
        this.sortData(this.sortBy, this.sortDirection);
    }

    sortData(fieldName, direction) {
        if (!this.cashflows || this.cashflows.length === 0) return;

        let parseData = JSON.parse(JSON.stringify(this.cashflows));
        // Return the value stored in the field
        let keyValue = (a) => {
            return a[fieldName];
        };
        // cheking reverse direction
        let isReverse = direction === 'asc' ? 1: -1;
        // sorting data
        parseData.sort((x, y) => {
            x = keyValue(x) ? keyValue(x) : ''; // handling null values
            y = keyValue(y) ? keyValue(y) : '';
            // sorting values based on direction
            return isReverse * ((x > y) - (y > x));
        });
        this.cashflows = parseData;
    }

    showToast(title, message, variant) {
        const event = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant,
        });
        this.dispatchEvent(event);
    }

    reduceErrors(errors) {
        if (!Array.isArray(errors)) {
            errors = [errors];
        }
        return (
            errors
                // Remove null/undefined items
                .filter((error) => !!error)
                // Extract an error message
                .map((error) => {
                    // UI API read errors
                    if (Array.isArray(error.body)) {
                        return error.body.map((e) => e.message);
                    }
                    // UI API DML, Apex and network errors
                    else if (error.body && typeof error.body.message === 'string') {
                        return error.body.message;
                    }
                    // JS errors
                    else if (typeof error.message === 'string') {
                        return error.message;
                    }
                    // Unknown error shape so try serializing
                    return error.statusText || JSON.stringify(error);
                })
                .reduce((prev, curr) => prev.concat(curr), [])
                .filter((message) => !!message)
        );
    }

    get errorMessage() {
        return this.error ? this.reduceErrors(this.error).join(', ') : '';
    }
}