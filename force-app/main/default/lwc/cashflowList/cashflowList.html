<template>
    <lightning-card title="Project Cashflows" icon-name="standard:investment_account">
        <div class="slds-m-around_medium">
            <template if:true={isLoading}>
                <lightning-spinner alternative-text="Loading cashflows..." size="medium"></lightning-spinner>
            </template>
            <template if:false={isLoading}>
                <template if:true={cashflows.length}>
                    <lightning-datatable
                        key-field="Id"
                        data={cashflows}
                        columns={columns}
                        hide-checkbox-column
                        onrowaction={handleRowAction}
                        sorted-by={sortBy}
                        sorted-direction={sortDirection}
                        onsort={handleSort}>
                    </lightning-datatable>
                </template>
                <template if:false={cashflows.length}>
                    <div class="slds-align_absolute-center slds-p-around_medium">
                        No cashflow records found for this project.
                    </div>
                </template>
            </template>
            <template if:true={error}>
                <div class="slds-text-color_error slds-p-around_medium">
                    Error loading cashflows: {errorMessage}
                </div>
            </template>
        </div>
    </lightning-card>
</template>